# AutoCRM Extension v3.0.0

**Simple, Reliable, Professional** - The perfect balance of functionality and maintainability.

## 🎯 Key Features

- **Custom Apertia Entity**: Complete CRUD operations with professional UI
- **Automated Naming**: Intelligent " - Apertia" suffix for Account and Apertia entities
- **Modern Architecture**: Uses EspoCRM 8+ standards with beforeSave hooks
- **Zero Dependencies**: No complex dependencies or configurations
- **Error Resilient**: Comprehensive error handling that never breaks functionality
- **Multi-language**: English and Czech language support

## 🚀 What's New in v3.0.0

### ✅ **Simplified Architecture**
- Removed complex dependency injection
- Eliminated over-engineering
- Reduced file count by 60%
- Streamlined for reliability

### ✅ **Modern Standards**
- Uses `beforeSave` hooks (no recursive save issues)
- PHP 8.1+ with strict typing
- Modern `str_ends_with()` function
- Professional error handling

### ✅ **Production Ready**
- Zero configuration needed
- Works out-of-the-box
- Comprehensive error protection
- Easy to maintain and debug

## 📋 Requirements

- **EspoCRM**: 8.0 or higher
- **PHP**: 8.1 or higher

## 🔧 Installation

1. Download `AutoCRM_Simple.zip`
2. Go to **Administration > Extensions**
3. Upload and install the extension
4. Clear cache: **Administration > Clear Cache**
5. **Done!** - Apertia appears in navigation automatically

## 💡 How It Works

### Automatic Naming
- **Account names** automatically get " - Apertia" suffix
- **Apertia names** automatically get " - Apertia" suffix  
- **Empty names** become "Ukázka - Apertia"
- **No duplicates** - suffix only added if not present

### Smart Hook System
- Uses `beforeSave` to prevent recursive saves
- Skips system operations (imports, etc.)
- Error-protected - never breaks save operations
- Modern PHP 8.1+ implementation

## 🏗️ Architecture

Based on the proven Custom approach with modern improvements:

```
Files/custom/Espo/Custom/
├── Controllers/Apertia.php          # Simple Base controller
├── Hooks/Account/ApertiaNameHook.php # beforeSave hook
├── Hooks/Apertia/ApertiaNameHook.php # beforeSave hook
├── Resources/metadata/              # Entity definitions
├── Resources/i18n/                  # Translations
└── Setup.php                        # Simple installation
```

### Key Design Principles
1. **Simplicity**: Minimal code, maximum reliability
2. **Standards**: Modern EspoCRM 8+ practices
3. **Safety**: Error protection everywhere
4. **Performance**: Fast, lightweight implementation

## 🔍 Troubleshooting

### Common Issues
- **Hooks not working**: Clear cache and rebuild
- **Navigation missing**: Manually add via Administration > User Interface
- **Naming not applied**: Check entity type and hook execution

### Debug Information
All errors are logged to EspoCRM error log with prefix "AutoCRM".

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

---

**AutoCRM v3.0.0** - *Simple, Reliable, Professional*
