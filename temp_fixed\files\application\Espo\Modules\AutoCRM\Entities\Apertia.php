<?php
/************************************************************************
 * This file is part of AutoCRM extension.
 *
 * AutoCRM is free software: you can redistribute it and/or modify
 * it under the terms of the MIT License.
 *
 * @package   AutoCRM
 * <AUTHOR> Development Team
 * @copyright 2025 AutoCRM
 * @license   MIT
 ************************************************************************/

declare(strict_types=1);

namespace Espo\Modules\AutoCRM\Entities;

use Espo\Core\ORM\Entity;

/**
 * Apertia Entity
 *
 * Represents an Apertia business entity with automated naming conventions,
 * status management, and comprehensive field validation.
 */
class Apertia extends Entity
{
    /** @var string Entity type identifier */
    public const ENTITY_TYPE = 'Apertia';

    /** @var string Draft status - initial state */
    public const STATUS_DRAFT = 'Draft';

    /** @var string Active status - in progress */
    public const STATUS_ACTIVE = 'Active';

    /** @var string Completed status - finished */
    public const STATUS_COMPLETED = 'Completed';

    /** @var string Canceled status - terminated */
    public const STATUS_CANCELED = 'Canceled';

    /** @var array Valid status values */
    public const VALID_STATUSES = [
        self::STATUS_DRAFT,
        self::STATUS_ACTIVE,
        self::STATUS_COMPLETED,
        self::STATUS_CANCELED
    ];

    /**
     * Get entity name
     */
    public function getName(): ?string
    {
        return $this->get('name');
    }

    /**
     * Set entity name with validation
     *
     * @throws \InvalidArgumentException
     */
    public function setName(?string $name): self
    {
        if ($name !== null && strlen($name) > 255) {
            throw new \InvalidArgumentException('Name cannot exceed 255 characters');
        }

        $this->set('name', $name);
        return $this;
    }

    /**
     * Get current status
     */
    public function getStatus(): ?string
    {
        return $this->get('status');
    }

    /**
     * Set status with validation
     *
     * @throws \InvalidArgumentException
     */
    public function setStatus(?string $status): self
    {
        if ($status !== null && !in_array($status, self::VALID_STATUSES, true)) {
            throw new \InvalidArgumentException("Invalid status: {$status}. Valid statuses: " . implode(', ', self::VALID_STATUSES));
        }

        $this->set('status', $status);
        return $this;
    }

    /**
     * Get assigned user ID
     */
    public function getAssignedUserId(): ?string
    {
        return $this->get('assignedUserId');
    }

    /**
     * Set assigned user ID
     */
    public function setAssignedUserId(?string $assignedUserId): self
    {
        $this->set('assignedUserId', $assignedUserId);
        return $this;
    }

    /**
     * Get description
     */
    public function getDescription(): ?string
    {
        return $this->get('description');
    }

    /**
     * Set description with validation
     *
     * @throws \InvalidArgumentException
     */
    public function setDescription(?string $description): self
    {
        if ($description !== null && strlen($description) > 10000) {
            throw new \InvalidArgumentException('Description cannot exceed 10000 characters');
        }

        $this->set('description', $description);
        return $this;
    }

    /**
     * Check if entity is in draft status
     */
    public function isDraft(): bool
    {
        return $this->getStatus() === self::STATUS_DRAFT;
    }

    /**
     * Check if entity is active
     */
    public function isActive(): bool
    {
        return $this->getStatus() === self::STATUS_ACTIVE;
    }

    /**
     * Check if entity is completed
     */
    public function isCompleted(): bool
    {
        return $this->getStatus() === self::STATUS_COMPLETED;
    }

    /**
     * Check if entity is canceled
     */
    public function isCanceled(): bool
    {
        return $this->getStatus() === self::STATUS_CANCELED;
    }

    /**
     * Get entity summary for logging/debugging
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'status' => $this->getStatus(),
            'assigned_user_id' => $this->getAssignedUserId(),
            'created_at' => $this->get('createdAt'),
            'modified_at' => $this->get('modifiedAt')
        ];
    }

    /**
     * Validate entity data
     *
     * @throws \InvalidArgumentException
     */
    public function validate(): bool
    {
        $errors = [];

        // Validate name
        $name = $this->getName();
        if (empty($name)) {
            $errors[] = 'Name is required';
        } elseif (strlen($name) > 255) {
            $errors[] = 'Name cannot exceed 255 characters';
        }

        // Validate status
        $status = $this->getStatus();
        if ($status !== null && !in_array($status, self::VALID_STATUSES, true)) {
            $errors[] = 'Invalid status value';
        }

        // Validate description length
        $description = $this->getDescription();
        if ($description !== null && strlen($description) > 10000) {
            $errors[] = 'Description cannot exceed 10000 characters';
        }

        if (!empty($errors)) {
            throw new \InvalidArgumentException('Validation failed: ' . implode(', ', $errors));
        }

        return true;
    }
}
