define('autocrm:views/lead/record/detail', ['crm:views/lead/record/detail'], function (Dep) {

    return Dep.extend({

        setup: function () {
            Dep.prototype.setup.call(this);

            // Add the Find Contacts button to dropdown menu
            this.addDropdownItem({
                name: 'findContacts',
                label: 'Find Contacts',
                action: 'findContacts'
            });
        },

        actionFindContacts: function () {
            const email = this.model.get('emailAddress');

            if (!email) {
                Espo.Ui.error(this.translate('No email address found for this lead.', 'messages', 'AutoCRM'));
                return;
            }

            // Validate email format
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                Espo.Ui.error(this.translate('Invalid email address format.', 'messages', 'AutoCRM'));
                return;
            }

            Espo.Ui.notify(this.translate('Searching...', 'messages', 'AutoCRM'));

            Espo.Ajax.postRequest('AutoCRM/FindContacts/findByEmail', {
                email: email
            }).then(response => {
                Espo.Ui.notify(false);

                const total = response.total || 0;
                const contacts = response.list || [];

                let message = '';
                if (total === 0) {
                    message = this.translate('No contacts found with email address: {email}', 'messages', 'AutoCRM')
                        .replace('{email}', email);

                    // Show simple alert for no results
                    Espo.Ui.alert(message);
                } else {
                    const names = contacts.map(contact => contact.name || this.translate('Unnamed', 'labels', 'Global')).join(', ');
                    message = this.translate('Found {count} contact(s) with email {email}:', 'messages', 'AutoCRM')
                        .replace('{count}', total)
                        .replace('{email}', email) + '\n\n' + names;

                    // Show browser alert with contact information
                    alert(message);

                    // Navigate to Contacts list and apply search after alert
                    this.navigateToContactsWithSearch(email);
                }

            }).catch(xhr => {
                Espo.Ui.notify(false);
                let message = this.translate('Error occurred while searching contacts.', 'messages', 'AutoCRM');
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                Espo.Ui.error(message);
            });
        },

        navigateToContactsWithSearch: function (email) {
            // Navigate to Contacts list
            this.getRouter().navigate('#Contact', {trigger: true});

            // Wait for the page to load, then apply search
            setTimeout(() => {
                this.applyContactSearch(email);
            }, 500);
        },

        applyContactSearch: function (email) {
            try {
                // Find the search input field
                const searchInput = document.querySelector('.text-filter[data-name="textFilter"]');
                if (searchInput) {
                    // Set the email value
                    searchInput.value = email;

                    // Trigger input event to update the view
                    const inputEvent = new Event('input', { bubbles: true });
                    searchInput.dispatchEvent(inputEvent);

                    // Find and click the search button
                    const searchButton = document.querySelector('.search[data-action="search"]');
                    if (searchButton) {
                        searchButton.click();
                    }
                } else {
                    // Fallback: try again after a longer delay
                    setTimeout(() => {
                        this.applyContactSearch(email);
                    }, 1000);
                }
            } catch (e) {
                console.error('Error applying contact search:', e);
            }
        }

    });

});
