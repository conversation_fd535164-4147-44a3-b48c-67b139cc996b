# AutoCRM Extension Development Guide

## 🏗️ ARCHITECTURE OVERVIEW

### Core Design Principles
The AutoCRM extension follows EspoCRM's module architecture with these key design decisions:

1. **Module Structure**: Uses `application/Espo/Modules/AutoCRM/` for proper namespace isolation
2. **Simplified Dependencies**: Avoids complex dependency injection to prevent runtime errors
3. **Error Resilience**: All operations wrapped in try-catch blocks with graceful degradation
4. **Professional Standards**: PHP 8.1+ with strict typing and comprehensive documentation

### File Organization
```
files/application/Espo/Modules/AutoCRM/
├── Controllers/
│   ├── Apertia.php              # CRUD operations for Apertia entity
│   └── FindContacts.php         # Contact search API endpoint
├── Entities/
│   └── Apertia.php              # ORM entity with validation
├── Hooks/
│   ├── Account/ApertiaNameHook.php   # Account naming automation
│   └── Apertia/ApertiaNameHook.php   # Apertia naming automation
├── Repositories/
│   └── Apertia.php              # Database operations
├── Resources/
│   ├── i18n/                    # Multi-language support
│   ├── layouts/                 # UI layouts
│   └── metadata/                # Entity definitions
└── Setup.php                    # Installation/uninstallation logic
```

## 🔧 TECHNICAL IMPLEMENTATION

### 1. Entity Design (Apertia.php)
- **Extends**: `Espo\Core\ORM\Entity`
- **Features**: Type-safe getters/setters, validation, status management
- **Constants**: Predefined status values and validation rules
- **Methods**: Business logic methods (isDraft, isActive, etc.)

### 2. Hook Architecture
- **Pattern**: Direct implementation without base classes
- **Error Handling**: Try-catch blocks prevent system crashes
- **Logic**: Automatic suffix appending with duplicate prevention
- **Performance**: Minimal overhead with early returns

### 3. Controller Implementation
- **Apertia Controller**: Extends `Espo\Core\Controllers\Record`
- **FindContacts Controller**: Custom API endpoint with ACL checks
- **Security**: Proper permission validation and input sanitization

### 4. Repository Pattern
- **Extends**: `Espo\Core\Repositories\Database`
- **Purpose**: Custom database operations and business logic
- **Integration**: Seamless ORM integration

### 5. Setup Management
- **Installation**: Navigation integration and cache management
- **Uninstallation**: Clean removal without breaking system
- **Error Handling**: Non-blocking error management

## 🎯 KEY DESIGN DECISIONS

### Why Simplified Architecture?
1. **Reliability**: Fewer dependencies = fewer failure points
2. **Maintainability**: Easier to debug and modify
3. **Performance**: Reduced overhead and faster execution
4. **Compatibility**: Works across different EspoCRM versions

### Error Handling Strategy
- **Non-blocking**: Errors logged but don't break functionality
- **Graceful Degradation**: System continues working even with hook failures
- **Comprehensive Logging**: Detailed error information for debugging

### Code Quality Standards
- **PHP 8.1+**: Modern PHP features and strict typing
- **PSR-12**: Coding standards compliance
- **Documentation**: Comprehensive PHPDoc comments
- **Validation**: Input validation and type safety

---

# 🚀 PROFESSIONAL DEVELOPMENT PROMPT

## Complete AutoCRM Extension Creation Prompt

You are a senior PHP developer with 15+ years of experience in EspoCRM development. Create a complete AutoCRM extension with the following specifications:

### PROJECT REQUIREMENTS

**Extension Name**: AutoCRM
**Version**: 2.0.0
**Target**: EspoCRM 8.0+
**PHP Version**: 8.1+

### CORE FUNCTIONALITY

1. **Custom Apertia Entity**
   - Complete CRUD operations
   - Status management (Draft, Active, Completed, Canceled)
   - Automatic naming with " - Apertia" suffix
   - Professional UI with proper layouts

2. **Account Enhancement**
   - Automatic " - Apertia" suffix for all Account names
   - Empty name handling with default "Ukázka - Apertia"
   - No duplicate suffixes

3. **Lead Contact Search**
   - Custom API endpoint for email-based contact search
   - Integration with Lead detail view
   - Professional UI button and modal

### TECHNICAL SPECIFICATIONS

#### 1. MODULE STRUCTURE
Create the following directory structure:
```
files/application/Espo/Modules/AutoCRM/
├── Controllers/
├── Entities/
├── Hooks/
├── Repositories/
├── Resources/
└── Setup.php
```

#### 2. ENTITY IMPLEMENTATION (Apertia.php)
```php
<?php
declare(strict_types=1);
namespace Espo\Modules\AutoCRM\Entities;
use Espo\Core\ORM\Entity;

class Apertia extends Entity
{
    public const ENTITY_TYPE = 'Apertia';
    public const STATUS_DRAFT = 'Draft';
    public const STATUS_ACTIVE = 'Active';
    public const STATUS_COMPLETED = 'Completed';
    public const STATUS_CANCELED = 'Canceled';
    public const VALID_STATUSES = [/* all statuses */];
    
    // Implement type-safe getters/setters
    // Add validation methods
    // Include business logic methods
}
```

#### 3. HOOK IMPLEMENTATION
Create hooks for Account and Apertia entities:
- **Pattern**: Direct implementation without inheritance
- **Error Handling**: Wrap all logic in try-catch blocks
- **Logic**: Check for existing suffix before appending
- **Performance**: Early returns for invalid entities

#### 4. CONTROLLER DESIGN
- **Apertia Controller**: Extend `Espo\Core\Controllers\Record`
- **FindContacts Controller**: Custom API with proper ACL checks
- **Security**: Validate all inputs and check permissions

#### 5. REPOSITORY PATTERN
- Extend `Espo\Core\Repositories\Database`
- Implement custom business logic
- Maintain ORM compatibility

#### 6. METADATA CONFIGURATION
Create complete metadata for:
- Entity definitions (entityDefs)
- Client definitions (clientDefs)
- Scope definitions (scopes)
- Layout definitions (layouts)

#### 7. INTERNATIONALIZATION
Support for:
- English (en_US)
- Czech (cs_CZ)
- Proper translation keys

#### 8. SETUP MANAGEMENT
```php
class Setup
{
    // Simple constructor with InjectableFactory
    // afterInstall(): Navigation integration + cache clear
    // afterUninstall(): Navigation cleanup + cache clear
    // Error handling: Non-blocking with logging
}
```

### CODE QUALITY REQUIREMENTS

1. **PHP Standards**
   - Use `declare(strict_types=1);`
   - Type hints for all parameters and return values
   - Proper namespace declarations

2. **Documentation**
   - Complete PHPDoc comments for all classes and methods
   - Professional file headers with license information
   - Inline comments for complex logic

3. **Error Handling**
   - Try-catch blocks around all critical operations
   - Graceful degradation on failures
   - Comprehensive error logging

4. **Security**
   - Input validation and sanitization
   - Proper ACL checks
   - XSS prevention

### MANIFEST REQUIREMENTS
```json
{
    "name": "AutoCRM",
    "version": "2.0.0",
    "author": "AutoCRM Development Team",
    "description": "Professional CRM extension...",
    "acceptableVersions": [">=8.0.0"],
    "skipBackup": false,
    "license": "MIT",
    "requirements": {
        "php": ">=8.1",
        "espocrm": ">=8.0.0"
    }
}
```

### CRITICAL SUCCESS FACTORS

1. **Reliability**: No Error 500 crashes under any circumstances
2. **Performance**: Minimal overhead and fast execution
3. **Maintainability**: Clean, readable, and well-documented code
4. **Extensibility**: Easy to modify and extend
5. **Professional Quality**: Production-ready code standards

### TESTING REQUIREMENTS

Ensure the extension handles:
- Empty entity names
- Existing suffixes
- System operations (imports, etc.)
- Permission restrictions
- Database errors
- Cache issues

### DELIVERABLES

1. Complete extension package (.zip)
2. Professional README.md
3. MIT License file
4. Installation instructions
5. Troubleshooting guide

### VALIDATION CHECKLIST

Before delivery, verify:
- ✅ No PHP errors or warnings
- ✅ All CRUD operations work
- ✅ Hooks execute properly
- ✅ Navigation integrates correctly
- ✅ Multi-language support works
- ✅ Installation/uninstallation is clean
- ✅ Error handling is comprehensive
- ✅ Code follows PSR-12 standards
- ✅ Documentation is complete

### SPECIFIC IMPLEMENTATION DETAILS

#### Hook Implementation Pattern
```php
class ApertiaNameHook
{
    public static $order = 5;

    public function beforeSave(Entity $entity, array $options = []): void
    {
        if ($entity->getEntityType() !== 'TARGET_ENTITY') return;
        if ($this->isSystemOperation($options)) return;

        try {
            $this->processNaming($entity);
        } catch (\Throwable $e) {
            error_log('AutoCRM Hook Error: ' . $e->getMessage());
        }
    }

    private function processNaming(Entity $entity): void
    {
        $name = $entity->get('name');
        $suffix = ' - Apertia';

        if (empty($name) || trim($name) === '') {
            $entity->set('name', 'Ukázka - Apertia');
            return;
        }

        $cleanName = trim($name);
        if (!$this->hasSuffix($cleanName, $suffix)) {
            $entity->set('name', $cleanName . $suffix);
        }
    }
}
```

#### Setup Class Pattern
```php
class Setup
{
    private InjectableFactory $injectableFactory;

    public function afterInstall(): void
    {
        try {
            $this->addToNavigation();
            $this->clearCache();
        } catch (\Throwable $e) {
            error_log('AutoCRM Setup: Installation failed - ' . $e->getMessage());
        }
    }
}
```

#### Entity Validation Pattern
```php
public function validate(): bool
{
    $errors = [];

    if (empty($this->getName())) {
        $errors[] = 'Name is required';
    }

    if (!empty($errors)) {
        throw new \InvalidArgumentException('Validation failed: ' . implode(', ', $errors));
    }

    return true;
}
```

This prompt will generate a production-ready AutoCRM extension that matches the quality and functionality of AutoCRM_Fixed.zip exactly.
