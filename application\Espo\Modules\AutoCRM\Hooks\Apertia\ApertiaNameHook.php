<?php

namespace Espo\Modules\AutoCRM\Hooks\Apertia;

use Espo\ORM\Entity;

class ApertiaNameHook
{
    public static $order = 5;

    public function beforeSave(Entity $entity, array $options = array())
    {
        if ($entity->getEntityType() !== 'Apertia') {
            return;
        }

        $name = $entity->get('name');
        
        if (empty($name)) {
            $entity->set('name', 'Ukázka - Apertia');
            return;
        }

        $name = trim($name);
        $suffix = ' - Apertia';
        
        if (strlen($name) < strlen($suffix) || substr($name, -strlen($suffix)) !== $suffix) {
            $entity->set('name', $name . $suffix);
        }
    }
}
