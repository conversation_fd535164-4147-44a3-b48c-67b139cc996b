<?php
/************************************************************************
 * This file is part of AutoCRM extension.
 * 
 * AutoCRM is free software: you can redistribute it and/or modify
 * it under the terms of the MIT License.
 * 
 * @package   AutoCRM
 * <AUTHOR> Development Team
 * @copyright 2025 AutoCRM
 * @license   MIT
 ************************************************************************/

declare(strict_types=1);

namespace Espo\Modules\AutoCRM\Core\Hooks;

use Espo\ORM\Entity;
use Espo\Modules\AutoCRM\Core\Config\AutoCRMConfig;
use Espo\Modules\AutoCRM\Core\Utils\Logger;

/**
 * Base Naming Hook
 * 
 * Abstract base class for entity naming hooks providing common functionality,
 * validation, error handling, and performance monitoring.
 */
abstract class BaseNamingHook
{
    protected AutoCRMConfig $config;
    protected Logger $logger;
    protected string $entityType;

    public function __construct()
    {
        $this->config = new AutoCRMConfig();
        $this->logger = new Logger(new \Espo\Core\Utils\Log(), static::class);
        $this->entityType = $this->getEntityType();
    }

    /**
     * Get the entity type this hook handles
     */
    abstract protected function getEntityType(): string;

    /**
     * Get hook execution order
     */
    public static function getOrder(): int
    {
        return (new AutoCRMConfig())->getHookOrder();
    }

    /**
     * Before save hook implementation
     */
    public function beforeSave(Entity $entity, array $options = []): void
    {
        $startTime = microtime(true);
        
        try {
            // Validate entity type
            if (!$this->isValidEntity($entity)) {
                return;
            }

            // Check if entity is enabled for processing
            if (!$this->config->isEntityEnabled($entity->getEntityType())) {
                $this->logger->debug("Entity type {$entity->getEntityType()} is disabled for naming hooks");
                return;
            }

            // Skip if this is a system operation
            if ($this->isSystemOperation($options)) {
                $this->logger->debug("Skipping naming hook for system operation");
                return;
            }

            $originalName = $entity->get('name');
            $this->processNaming($entity);
            $newName = $entity->get('name');

            // Log the change if name was modified
            if ($originalName !== $newName) {
                $this->logger->logHookExecution(
                    $entity->getEntityType(),
                    'beforeSave',
                    'name_updated',
                    [
                        'original_name' => $originalName,
                        'new_name' => $newName,
                        'entity_id' => $entity->getId()
                    ]
                );
            }

        } catch (\Throwable $e) {
            $this->logger->logException($e, [
                'entity_type' => $entity->getEntityType(),
                'entity_id' => $entity->getId(),
                'hook_class' => static::class
            ]);
            
            // Don't re-throw to avoid breaking the save operation
            // Log the error and continue
        } finally {
            $executionTime = microtime(true) - $startTime;
            $this->logger->logPerformance('naming_hook_execution', $executionTime, [
                'entity_type' => $entity->getEntityType(),
                'hook_class' => static::class
            ]);
        }
    }

    /**
     * Process entity naming logic
     */
    protected function processNaming(Entity $entity): void
    {
        $name = $entity->get('name');
        
        // Handle empty names
        if ($this->isEmpty($name)) {
            $entity->set('name', $this->config->getEmptyName());
            return;
        }

        // Clean and validate name
        $cleanName = $this->cleanName($name);
        if (!$this->isValidName($cleanName)) {
            $this->logger->warning("Invalid name detected", [
                'original_name' => $name,
                'cleaned_name' => $cleanName,
                'entity_type' => $entity->getEntityType()
            ]);
            return;
        }

        // Apply suffix if not already present
        $suffix = $this->config->getApertiaSuffix();
        if (!$this->hasSuffix($cleanName, $suffix)) {
            $entity->set('name', $cleanName . $suffix);
        }
    }

    /**
     * Validate if entity should be processed
     */
    protected function isValidEntity(Entity $entity): bool
    {
        return $entity->getEntityType() === $this->entityType;
    }

    /**
     * Check if name is empty or whitespace only
     */
    protected function isEmpty(?string $name): bool
    {
        return empty($name) || trim($name) === '';
    }

    /**
     * Clean and normalize name
     */
    protected function cleanName(string $name): string
    {
        // Trim whitespace
        $cleaned = trim($name);
        
        // Remove multiple consecutive spaces
        $cleaned = preg_replace('/\s+/', ' ', $cleaned);
        
        // Remove potentially dangerous characters
        $cleaned = preg_replace('/[<>"\']/', '', $cleaned);
        
        return $cleaned;
    }

    /**
     * Validate name meets requirements
     */
    protected function isValidName(string $name): bool
    {
        // Check minimum length
        if (strlen($name) < 1) {
            return false;
        }
        
        // Check maximum length (accounting for suffix)
        $maxLength = 255 - strlen($this->config->getApertiaSuffix());
        if (strlen($name) > $maxLength) {
            return false;
        }
        
        // Check for valid characters (allow unicode)
        if (!preg_match('/^[\p{L}\p{N}\p{P}\p{S}\p{Z}]+$/u', $name)) {
            return false;
        }
        
        return true;
    }

    /**
     * Check if name already has the suffix
     */
    protected function hasSuffix(string $name, string $suffix): bool
    {
        $suffixLength = strlen($suffix);
        return strlen($name) >= $suffixLength && 
               substr($name, -$suffixLength) === $suffix;
    }

    /**
     * Check if this is a system operation that should be skipped
     */
    protected function isSystemOperation(array $options): bool
    {
        return isset($options['skipHooks']) && $options['skipHooks'] === true ||
               isset($options['silent']) && $options['silent'] === true ||
               isset($options['import']) && $options['import'] === true;
    }

    /**
     * Get hook metadata for debugging
     */
    public function getMetadata(): array
    {
        return [
            'class' => static::class,
            'entity_type' => $this->entityType,
            'order' => static::getOrder(),
            'config' => [
                'suffix' => $this->config->getApertiaSuffix(),
                'empty_name' => $this->config->getEmptyName(),
                'enabled_entities' => $this->config->getEnabledEntities()
            ]
        ];
    }
}
