<?php
/************************************************************************
 * This file is part of AutoCRM extension.
 * 
 * @package   AutoCRM
 * <AUTHOR> Development Team
 * @copyright 2025 AutoCRM
 * @license   MIT
 ************************************************************************/

declare(strict_types=1);

namespace Espo\Custom\Hooks\Account;

use Espo\ORM\Entity;

/**
 * Account Apertia Naming Hook
 * 
 * Modern, reliable hook using beforeSave to prevent recursive saves.
 * Automatically appends " - Apertia" suffix to Account names.
 */
class ApertiaNameHook
{
    public static int $order = 5;

    /**
     * Before save hook - processes naming before entity is saved
     * This prevents the recursive save issues that can occur with afterSave
     */
    public function beforeSave(Entity $entity, array $options = []): void
    {
        // Early return for performance and safety
        if ($entity->getEntityType() !== 'Account') {
            return;
        }

        // Skip system operations to prevent conflicts during imports, etc.
        if ($this->isSystemOperation($options)) {
            return;
        }

        // Process naming with comprehensive error protection
        try {
            $this->processNaming($entity);
        } catch (\Throwable $e) {
            // Log error but don't break the save operation
            // This ensures the system continues working even if naming fails
            error_log('AutoCRM Account Hook Error: ' . $e->getMessage());
        }
    }

    /**
     * Process entity naming logic with modern PHP standards
     */
    private function processNaming(Entity $entity): void
    {
        $name = $entity->get('name');
        $suffix = ' - Apertia';

        // Handle empty names with a default value
        if (empty($name) || trim($name) === '') {
            $entity->set('name', 'Ukázka - Apertia');
            return;
        }

        // Clean the name and check for existing suffix
        $cleanName = trim($name);

        // Use modern PHP 8.1+ str_ends_with function for better performance
        if (!str_ends_with($cleanName, $suffix)) {
            $entity->set('name', $cleanName . $suffix);
        }
    }

    /**
     * Check if this is a system operation that should be skipped
     * This prevents conflicts during imports, mass updates, etc.
     */
    private function isSystemOperation(array $options): bool
    {
        return isset($options['skipHooks']) && $options['skipHooks'] === true ||
               isset($options['silent']) && $options['silent'] === true ||
               isset($options['import']) && $options['import'] === true;
    }
}
