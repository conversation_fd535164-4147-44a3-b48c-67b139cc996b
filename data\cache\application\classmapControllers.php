<?php
return [
  'ActionHistoryRecord' => 'Espo\\Controllers\\ActionHistoryRecord',
  'AddressCountry' => 'Espo\\Controllers\\AddressCountry',
  'Admin' => 'Espo\\Controllers\\Admin',
  'ApiIndex' => 'Espo\\Controllers\\ApiIndex',
  'AppLogRecord' => 'Espo\\Controllers\\AppLogRecord',
  'AppSecret' => 'Espo\\Controllers\\AppSecret',
  'Attachment' => 'Espo\\Controllers\\Attachment',
  'AuthLogRecord' => 'Espo\\Controllers\\AuthLogRecord',
  'AuthToken' => 'Espo\\Controllers\\AuthToken',
  'AuthenticationProvider' => 'Espo\\Controllers\\AuthenticationProvider',
  'DashboardTemplate' => 'Espo\\Controllers\\DashboardTemplate',
  'DataPrivacy' => 'Espo\\Controllers\\DataPrivacy',
  'Email' => 'Espo\\Controllers\\Email',
  'EmailAccount' => 'Espo\\Controllers\\EmailAccount',
  'EmailAddress' => 'Espo\\Controllers\\EmailAddress',
  'EmailFilter' => 'Espo\\Controllers\\EmailFilter',
  'EmailFolder' => 'Espo\\Controllers\\EmailFolder',
  'EmailTemplate' => 'Espo\\Controllers\\EmailTemplate',
  'EmailTemplateCategory' => 'Espo\\Controllers\\EmailTemplateCategory',
  'EntityManager' => 'Espo\\Controllers\\EntityManager',
  'Extension' => 'Espo\\Controllers\\Extension',
  'ExternalAccount' => 'Espo\\Controllers\\ExternalAccount',
  'FieldManager' => 'Espo\\Controllers\\FieldManager',
  'Formula' => 'Espo\\Controllers\\Formula',
  'GroupEmailFolder' => 'Espo\\Controllers\\GroupEmailFolder',
  'I18n' => 'Espo\\Controllers\\I18n',
  'Import' => 'Espo\\Controllers\\Import',
  'ImportError' => 'Espo\\Controllers\\ImportError',
  'InboundEmail' => 'Espo\\Controllers\\InboundEmail',
  'Integration' => 'Espo\\Controllers\\Integration',
  'Job' => 'Espo\\Controllers\\Job',
  'LabelManager' => 'Espo\\Controllers\\LabelManager',
  'LastViewed' => 'Espo\\Controllers\\LastViewed',
  'Layout' => 'Espo\\Controllers\\Layout',
  'LayoutSet' => 'Espo\\Controllers\\LayoutSet',
  'Ldap' => 'Espo\\Controllers\\Ldap',
  'LeadCapture' => 'Espo\\Controllers\\LeadCapture',
  'LeadCaptureLogRecord' => 'Espo\\Controllers\\LeadCaptureLogRecord',
  'Metadata' => 'Espo\\Controllers\\Metadata',
  'Note' => 'Espo\\Controllers\\Note',
  'Notification' => 'Espo\\Controllers\\Notification',
  'OAuthAccount' => 'Espo\\Controllers\\OAuthAccount',
  'OAuthProvider' => 'Espo\\Controllers\\OAuthProvider',
  'Oidc' => 'Espo\\Controllers\\Oidc',
  'Pdf' => 'Espo\\Controllers\\Pdf',
  'PhoneNumber' => 'Espo\\Controllers\\PhoneNumber',
  'PopupNotification' => 'Espo\\Controllers\\PopupNotification',
  'Portal' => 'Espo\\Controllers\\Portal',
  'PortalRole' => 'Espo\\Controllers\\PortalRole',
  'Preferences' => 'Espo\\Controllers\\Preferences',
  'Role' => 'Espo\\Controllers\\Role',
  'ScheduledJob' => 'Espo\\Controllers\\ScheduledJob',
  'ScheduledJobLogRecord' => 'Espo\\Controllers\\ScheduledJobLogRecord',
  'Settings' => 'Espo\\Controllers\\Settings',
  'Stream' => 'Espo\\Controllers\\Stream',
  'Team' => 'Espo\\Controllers\\Team',
  'Template' => 'Espo\\Controllers\\Template',
  'TemplateManager' => 'Espo\\Controllers\\TemplateManager',
  'TwoFactorEmail' => 'Espo\\Controllers\\TwoFactorEmail',
  'TwoFactorSms' => 'Espo\\Controllers\\TwoFactorSms',
  'User' => 'Espo\\Controllers\\User',
  'UserSecurity' => 'Espo\\Controllers\\UserSecurity',
  'Webhook' => 'Espo\\Controllers\\Webhook',
  'WebhookEventQueueItem' => 'Espo\\Controllers\\WebhookEventQueueItem',
  'WebhookQueueItem' => 'Espo\\Controllers\\WebhookQueueItem',
  'WorkingTimeCalendar' => 'Espo\\Controllers\\WorkingTimeCalendar',
  'WorkingTimeRange' => 'Espo\\Controllers\\WorkingTimeRange',
  'Account' => 'Espo\\Modules\\Crm\\Controllers\\Account',
  'Activities' => 'Espo\\Modules\\Crm\\Controllers\\Activities',
  'Call' => 'Espo\\Modules\\Crm\\Controllers\\Call',
  'Campaign' => 'Espo\\Modules\\Crm\\Controllers\\Campaign',
  'CampaignLogRecord' => 'Espo\\Modules\\Crm\\Controllers\\CampaignLogRecord',
  'CampaignTrackingUrl' => 'Espo\\Modules\\Crm\\Controllers\\CampaignTrackingUrl',
  'Case' => 'Espo\\Modules\\Crm\\Controllers\\CaseObj',
  'Contact' => 'Espo\\Modules\\Crm\\Controllers\\Contact',
  'Document' => 'Espo\\Modules\\Crm\\Controllers\\Document',
  'DocumentFolder' => 'Espo\\Modules\\Crm\\Controllers\\DocumentFolder',
  'EmailQueueItem' => 'Espo\\Modules\\Crm\\Controllers\\EmailQueueItem',
  'KnowledgeBaseArticle' => 'Espo\\Modules\\Crm\\Controllers\\KnowledgeBaseArticle',
  'KnowledgeBaseCategory' => 'Espo\\Modules\\Crm\\Controllers\\KnowledgeBaseCategory',
  'Lead' => 'Espo\\Modules\\Crm\\Controllers\\Lead',
  'MassEmail' => 'Espo\\Modules\\Crm\\Controllers\\MassEmail',
  'Meeting' => 'Espo\\Modules\\Crm\\Controllers\\Meeting',
  'Opportunity' => 'Espo\\Modules\\Crm\\Controllers\\Opportunity',
  'Target' => 'Espo\\Modules\\Crm\\Controllers\\Target',
  'TargetList' => 'Espo\\Modules\\Crm\\Controllers\\TargetList',
  'TargetListCategory' => 'Espo\\Modules\\Crm\\Controllers\\TargetListCategory',
  'Task' => 'Espo\\Modules\\Crm\\Controllers\\Task',
  'Apertia' => 'Espo\\Modules\\AutoCRM\\Controllers\\Apertia',
  'FindContacts' => 'Espo\\Modules\\AutoCRM\\Controllers\\FindContacts'
];
