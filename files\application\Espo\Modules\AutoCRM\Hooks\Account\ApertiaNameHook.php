<?php
/************************************************************************
 * This file is part of AutoCRM extension.
 * 
 * @package   AutoCRM
 * <AUTHOR> Development Team
 * @copyright 2025 AutoCRM
 * @license   MIT
 ************************************************************************/

declare(strict_types=1);

namespace Espo\Modules\AutoCRM\Hooks\Account;

use Espo\ORM\Entity;

/**
 * Account Apertia Naming Hook
 * 
 * Simple, reliable hook that automatically appends " - Apertia" suffix
 * to Account names using beforeSave to prevent recursive saves.
 */
class ApertiaNameHook
{
    public static int $order = 5;

    /**
     * Before save hook - processes naming before entity is saved
     */
    public function beforeSave(Entity $entity, array $options = []): void
    {
        // Early returns for performance and safety
        if ($entity->getEntityType() !== 'Account') {
            return;
        }

        // Skip system operations to prevent conflicts
        if ($this->isSystemOperation($options)) {
            return;
        }

        // Process naming with error protection
        try {
            $this->processNaming($entity);
        } catch (\Throwable $e) {
            // Log error but don't break the save operation
            error_log('AutoCRM Account Hook Error: ' . $e->getMessage());
        }
    }

    /**
     * Process entity naming logic
     */
    private function processNaming(Entity $entity): void
    {
        $name = $entity->get('name');
        $suffix = ' - Apertia';

        // Handle empty names with default value
        if (empty($name) || trim($name) === '') {
            $entity->set('name', 'Ukázka - Apertia');
            return;
        }

        // Clean the name
        $cleanName = trim($name);

        // Add suffix if not already present
        if (!str_ends_with($cleanName, $suffix)) {
            $entity->set('name', $cleanName . $suffix);
        }
    }

    /**
     * Check if this is a system operation that should be skipped
     */
    private function isSystemOperation(array $options): bool
    {
        return isset($options['skipHooks']) && $options['skipHooks'] === true ||
               isset($options['silent']) && $options['silent'] === true ||
               isset($options['import']) && $options['import'] === true;
    }
}
