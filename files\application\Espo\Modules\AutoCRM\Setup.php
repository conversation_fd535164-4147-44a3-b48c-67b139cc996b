<?php
/************************************************************************
 * This file is part of AutoCRM extension.
 * 
 * @package   AutoCRM
 * <AUTHOR> Development Team
 * @copyright 2025 AutoCRM
 * @license   MIT
 ************************************************************************/

declare(strict_types=1);

namespace Espo\Modules\AutoCRM;

use Espo\Core\Utils\Config;
use Espo\Core\Utils\Config\ConfigWriter;
use Espo\Core\InjectableFactory;

/**
 * AutoCRM Setup Manager
 * 
 * Simple, reliable installation and uninstallation with minimal dependencies.
 */
class Setup
{
    private InjectableFactory $injectableFactory;

    public function __construct(InjectableFactory $injectableFactory)
    {
        $this->injectableFactory = $injectableFactory;
    }

    /**
     * Called after extension installation
     */
    public function afterInstall(): void
    {
        try {
            $this->addToNavigation();
            $this->clearCache();
        } catch (\Throwable $e) {
            // Log error but don't break installation
            error_log('AutoCRM Setup: Installation failed - ' . $e->getMessage());
        }
    }

    /**
     * Called after extension uninstallation
     */
    public function afterUninstall(): void
    {
        try {
            $this->removeFromNavigation();
            $this->clearCache();
        } catch (\Throwable $e) {
            // Log error but don't break uninstallation
            error_log('AutoCRM Setup: Uninstallation failed - ' . $e->getMessage());
        }
    }

    /**
     * Add Apertia entity to navigation
     */
    private function addToNavigation(): void
    {
        $config = $this->injectableFactory->create(Config::class);
        $configWriter = $this->injectableFactory->create(ConfigWriter::class);

        $tabList = $config->get('tabList', []);

        // Add Apertia to navigation if not already present
        if (!in_array('Apertia', $tabList, true)) {
            // Try to insert after Account, otherwise add at end
            $insertPosition = count($tabList);
            $accountIndex = array_search('Account', $tabList, true);
            if ($accountIndex !== false) {
                $insertPosition = $accountIndex + 1;
            }

            array_splice($tabList, $insertPosition, 0, ['Apertia']);
            $configWriter->set('tabList', $tabList);
            $configWriter->save();
        }
    }

    /**
     * Remove Apertia entity from navigation
     */
    private function removeFromNavigation(): void
    {
        $config = $this->injectableFactory->create(Config::class);
        $configWriter = $this->injectableFactory->create(ConfigWriter::class);

        $tabList = $config->get('tabList', []);

        // Remove Apertia from navigation if present
        $apertiaIndex = array_search('Apertia', $tabList, true);
        if ($apertiaIndex !== false) {
            array_splice($tabList, $apertiaIndex, 1);
            $configWriter->set('tabList', $tabList);
            $configWriter->save();
        }
    }

    /**
     * Clear cache after installation/uninstallation
     */
    private function clearCache(): void
    {
        $configWriter = $this->injectableFactory->create(ConfigWriter::class);
        
        // Force cache rebuild
        $configWriter->set('cacheTimestamp', time());
        $configWriter->save();
    }
}
