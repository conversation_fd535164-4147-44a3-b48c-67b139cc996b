{"fields": {"name": {"type": "<PERSON><PERSON><PERSON>", "maxLength": 255, "required": true, "trim": true}, "status": {"type": "enum", "options": ["Draft", "Active", "Completed", "Canceled"], "default": "Draft", "style": {"Draft": "default", "Active": "success", "Completed": "primary", "Canceled": "danger"}}, "assignedUser": {"type": "link", "view": "views/fields/assigned-user"}, "teams": {"type": "linkMultiple", "view": "views/fields/teams"}, "description": {"type": "text", "rows": 4}, "createdAt": {"type": "datetime", "readOnly": true}, "modifiedAt": {"type": "datetime", "readOnly": true}, "createdBy": {"type": "link", "readOnly": true, "view": "views/fields/user"}, "modifiedBy": {"type": "link", "readOnly": true, "view": "views/fields/user"}}, "links": {"assignedUser": {"type": "belongsTo", "entity": "User"}, "teams": {"type": "hasMany", "entity": "Team", "relationName": "entityTeam", "layoutRelationshipsDisabled": true}, "createdBy": {"type": "belongsTo", "entity": "User"}, "modifiedBy": {"type": "belongsTo", "entity": "User"}}, "collection": {"orderBy": "createdAt", "order": "desc", "textFilterFields": ["name", "description"]}, "indexes": {"name": {"columns": ["name", "deleted"]}, "assignedUser": {"columns": ["assignedUserId", "deleted"]}}}