<?php
/************************************************************************
 * This file is part of AutoCRM extension.
 * 
 * AutoCRM is free software: you can redistribute it and/or modify
 * it under the terms of the MIT License.
 * 
 * @package   AutoCRM
 * <AUTHOR> Development Team
 * @copyright 2025 AutoCRM
 * @license   MIT
 ************************************************************************/

declare(strict_types=1);

namespace Espo\Modules\AutoCRM\Core\Utils;

use Espo\Core\Utils\Log;
use Psr\Log\LoggerInterface;

/**
 * AutoCRM Logger
 * 
 * Enhanced logging service for AutoCRM extension with structured logging,
 * context management, and performance monitoring.
 */
class Logger
{
    private LoggerInterface $logger;
    private string $component;
    private array $context;

    public function __construct(Log $log, string $component = 'AutoCRM')
    {
        $this->logger = $log;
        $this->component = $component;
        $this->context = [
            'module' => 'AutoCRM',
            'component' => $component,
            'version' => '2.0.0'
        ];
    }

    /**
     * Log info message with context
     */
    public function info(string $message, array $context = []): void
    {
        $this->log('info', $message, $context);
    }

    /**
     * Log warning message with context
     */
    public function warning(string $message, array $context = []): void
    {
        $this->log('warning', $message, $context);
    }

    /**
     * Log error message with context
     */
    public function error(string $message, array $context = []): void
    {
        $this->log('error', $message, $context);
    }

    /**
     * Log debug message with context
     */
    public function debug(string $message, array $context = []): void
    {
        $this->log('debug', $message, $context);
    }

    /**
     * Log hook execution
     */
    public function logHookExecution(string $entityType, string $hookType, string $action, array $context = []): void
    {
        $message = "Hook executed: {$hookType} on {$entityType} - {$action}";
        $fullContext = array_merge($context, [
            'entity_type' => $entityType,
            'hook_type' => $hookType,
            'action' => $action,
            'execution_time' => microtime(true)
        ]);
        
        $this->info($message, $fullContext);
    }

    /**
     * Log configuration changes
     */
    public function logConfigChange(string $key, $oldValue, $newValue): void
    {
        $message = "Configuration changed: {$key}";
        $context = [
            'config_key' => $key,
            'old_value' => $this->sanitizeValue($oldValue),
            'new_value' => $this->sanitizeValue($newValue),
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        $this->info($message, $context);
    }

    /**
     * Log performance metrics
     */
    public function logPerformance(string $operation, float $executionTime, array $metrics = []): void
    {
        $message = "Performance: {$operation} completed in {$executionTime}ms";
        $context = array_merge($metrics, [
            'operation' => $operation,
            'execution_time_ms' => round($executionTime * 1000, 2),
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ]);
        
        if ($executionTime > 1.0) { // Log as warning if > 1 second
            $this->warning($message, $context);
        } else {
            $this->debug($message, $context);
        }
    }

    /**
     * Log exception with full context
     */
    public function logException(\Throwable $exception, array $context = []): void
    {
        $message = "Exception: {$exception->getMessage()}";
        $fullContext = array_merge($context, [
            'exception_class' => get_class($exception),
            'exception_message' => $exception->getMessage(),
            'exception_code' => $exception->getCode(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $this->sanitizeTrace($exception->getTrace())
        ]);
        
        $this->error($message, $fullContext);
    }

    /**
     * Create child logger for specific component
     */
    public function forComponent(string $component): self
    {
        return new self($this->logger, $component);
    }

    /**
     * Add persistent context to all log messages
     */
    public function withContext(array $context): self
    {
        $clone = clone $this;
        $clone->context = array_merge($this->context, $context);
        return $clone;
    }

    /**
     * Internal logging method
     */
    private function log(string $level, string $message, array $context = []): void
    {
        $fullMessage = "[{$this->component}] {$message}";
        $fullContext = array_merge($this->context, $context);
        
        $this->logger->log($level, $fullMessage, $fullContext);
    }

    /**
     * Sanitize sensitive values for logging
     */
    private function sanitizeValue($value): string
    {
        if (is_string($value) && strlen($value) > 100) {
            return substr($value, 0, 100) . '...';
        }
        
        if (is_array($value)) {
            return '[Array with ' . count($value) . ' items]';
        }
        
        if (is_object($value)) {
            return '[Object: ' . get_class($value) . ']';
        }
        
        return (string) $value;
    }

    /**
     * Sanitize stack trace for logging
     */
    private function sanitizeTrace(array $trace): array
    {
        return array_slice(array_map(function ($item) {
            return [
                'file' => $item['file'] ?? 'unknown',
                'line' => $item['line'] ?? 0,
                'function' => $item['function'] ?? 'unknown',
                'class' => $item['class'] ?? null
            ];
        }, $trace), 0, 5); // Limit to first 5 stack frames
    }
}
