<?php
/************************************************************************
 * This file is part of AutoCRM extension.
 * 
 * AutoCRM is free software: you can redistribute it and/or modify
 * it under the terms of the MIT License.
 * 
 * @package   AutoCRM
 * <AUTHOR> Development Team
 * @copyright 2025 AutoCRM
 * @license   MIT
 ************************************************************************/

declare(strict_types=1);

namespace Espo\Modules\AutoCRM\Core\Config;

/**
 * AutoCRM Configuration Manager
 * 
 * Centralized configuration management for AutoCRM extension
 * providing type-safe configuration access and validation.
 */
class AutoCRMConfig
{
    /** @var string Default suffix for Apertia naming */
    public const DEFAULT_APERTIA_SUFFIX = ' - Apertia';
    
    /** @var string Default name when empty */
    public const DEFAULT_EMPTY_NAME = 'Ukázka - Apertia';
    
    /** @var int Default hook execution order */
    public const DEFAULT_HOOK_ORDER = 5;
    
    /** @var array Default navigation position preferences */
    public const NAVIGATION_PREFERENCES = [
        'insertAfter' => 'Account',
        'fallbackPosition' => 'end'
    ];
    
    /** @var array Supported entity types for naming hooks */
    public const SUPPORTED_ENTITIES = ['Account', 'Apertia'];
    
    /** @var array Configuration validation rules */
    private const VALIDATION_RULES = [
        'apertia_suffix' => ['type' => 'string', 'maxLength' => 50],
        'empty_name' => ['type' => 'string', 'maxLength' => 100],
        'hook_order' => ['type' => 'integer', 'min' => 1, 'max' => 100],
        'enabled_entities' => ['type' => 'array']
    ];

    private array $config;

    public function __construct(array $config = [])
    {
        $this->config = array_merge($this->getDefaults(), $config);
        $this->validate();
    }

    /**
     * Get Apertia suffix for naming
     */
    public function getApertiaSuffix(): string
    {
        return $this->config['apertia_suffix'] ?? self::DEFAULT_APERTIA_SUFFIX;
    }

    /**
     * Get default name for empty entities
     */
    public function getEmptyName(): string
    {
        return $this->config['empty_name'] ?? self::DEFAULT_EMPTY_NAME;
    }

    /**
     * Get hook execution order
     */
    public function getHookOrder(): int
    {
        return $this->config['hook_order'] ?? self::DEFAULT_HOOK_ORDER;
    }

    /**
     * Get enabled entity types
     */
    public function getEnabledEntities(): array
    {
        return $this->config['enabled_entities'] ?? self::SUPPORTED_ENTITIES;
    }

    /**
     * Check if entity type is enabled for naming hooks
     */
    public function isEntityEnabled(string $entityType): bool
    {
        return in_array($entityType, $this->getEnabledEntities(), true);
    }

    /**
     * Get navigation preferences
     */
    public function getNavigationPreferences(): array
    {
        return $this->config['navigation'] ?? self::NAVIGATION_PREFERENCES;
    }

    /**
     * Get default configuration values
     */
    private function getDefaults(): array
    {
        return [
            'apertia_suffix' => self::DEFAULT_APERTIA_SUFFIX,
            'empty_name' => self::DEFAULT_EMPTY_NAME,
            'hook_order' => self::DEFAULT_HOOK_ORDER,
            'enabled_entities' => self::SUPPORTED_ENTITIES,
            'navigation' => self::NAVIGATION_PREFERENCES
        ];
    }

    /**
     * Validate configuration values
     * 
     * @throws \InvalidArgumentException
     */
    private function validate(): void
    {
        foreach (self::VALIDATION_RULES as $key => $rules) {
            if (!isset($this->config[$key])) {
                continue;
            }

            $value = $this->config[$key];

            // Type validation
            if (isset($rules['type'])) {
                $expectedType = $rules['type'];
                $actualType = gettype($value);
                
                if ($expectedType === 'integer' && $actualType !== 'integer') {
                    throw new \InvalidArgumentException("Config '{$key}' must be integer, {$actualType} given");
                }
                
                if ($expectedType === 'string' && $actualType !== 'string') {
                    throw new \InvalidArgumentException("Config '{$key}' must be string, {$actualType} given");
                }
                
                if ($expectedType === 'array' && $actualType !== 'array') {
                    throw new \InvalidArgumentException("Config '{$key}' must be array, {$actualType} given");
                }
            }

            // String length validation
            if (isset($rules['maxLength']) && is_string($value)) {
                if (strlen($value) > $rules['maxLength']) {
                    throw new \InvalidArgumentException("Config '{$key}' exceeds maximum length of {$rules['maxLength']}");
                }
            }

            // Integer range validation
            if (isset($rules['min']) && is_int($value) && $value < $rules['min']) {
                throw new \InvalidArgumentException("Config '{$key}' must be at least {$rules['min']}");
            }
            
            if (isset($rules['max']) && is_int($value) && $value > $rules['max']) {
                throw new \InvalidArgumentException("Config '{$key}' must be at most {$rules['max']}");
            }
        }
    }
}
