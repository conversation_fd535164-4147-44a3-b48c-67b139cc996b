<?php
return [
  'ActionHistoryRecord' => 'Espo\\Entities\\ActionHistoryRecord',
  'AddressCountry' => 'Espo\\Entities\\AddressCountry',
  'AppLogRecord' => 'Espo\\Entities\\AppLogRecord',
  'AppSecret' => 'Espo\\Entities\\AppSecret',
  'ArrayValue' => 'Espo\\Entities\\ArrayValue',
  'Attachment' => 'Espo\\Entities\\Attachment',
  'AuthLogRecord' => 'Espo\\Entities\\AuthLogRecord',
  'AuthToken' => 'Espo\\Entities\\AuthToken',
  'AuthenticationProvider' => 'Espo\\Entities\\AuthenticationProvider',
  'Autofollow' => 'Espo\\Entities\\Autofollow',
  'Currency' => 'Espo\\Entities\\Currency',
  'DashboardTemplate' => 'Espo\\Entities\\DashboardTemplate',
  'Email' => 'Espo\\Entities\\Email',
  'EmailAccount' => 'Espo\\Entities\\EmailAccount',
  'EmailAddress' => 'Espo\\Entities\\EmailAddress',
  'EmailFilter' => 'Espo\\Entities\\EmailFilter',
  'EmailFolder' => 'Espo\\Entities\\EmailFolder',
  'EmailTemplate' => 'Espo\\Entities\\EmailTemplate',
  'EmailTemplateCategory' => 'Espo\\Entities\\EmailTemplateCategory',
  'Export' => 'Espo\\Entities\\Export',
  'Extension' => 'Espo\\Entities\\Extension',
  'ExternalAccount' => 'Espo\\Entities\\ExternalAccount',
  'GroupEmailFolder' => 'Espo\\Entities\\GroupEmailFolder',
  'Import' => 'Espo\\Entities\\Import',
  'ImportEntity' => 'Espo\\Entities\\ImportEntity',
  'ImportError' => 'Espo\\Entities\\ImportError',
  'InboundEmail' => 'Espo\\Entities\\InboundEmail',
  'Integration' => 'Espo\\Entities\\Integration',
  'Job' => 'Espo\\Entities\\Job',
  'KanbanOrder' => 'Espo\\Entities\\KanbanOrder',
  'LayoutRecord' => 'Espo\\Entities\\LayoutRecord',
  'LayoutSet' => 'Espo\\Entities\\LayoutSet',
  'LeadCapture' => 'Espo\\Entities\\LeadCapture',
  'LeadCaptureLogRecord' => 'Espo\\Entities\\LeadCaptureLogRecord',
  'MassAction' => 'Espo\\Entities\\MassAction',
  'NextNumber' => 'Espo\\Entities\\NextNumber',
  'Note' => 'Espo\\Entities\\Note',
  'Notification' => 'Espo\\Entities\\Notification',
  'OAuthAccount' => 'Espo\\Entities\\OAuthAccount',
  'OAuthProvider' => 'Espo\\Entities\\OAuthProvider',
  'PasswordChangeRequest' => 'Espo\\Entities\\PasswordChangeRequest',
  'PhoneNumber' => 'Espo\\Entities\\PhoneNumber',
  'Portal' => 'Espo\\Entities\\Portal',
  'PortalRole' => 'Espo\\Entities\\PortalRole',
  'Preferences' => 'Espo\\Entities\\Preferences',
  'Role' => 'Espo\\Entities\\Role',
  'ScheduledJob' => 'Espo\\Entities\\ScheduledJob',
  'ScheduledJobLogRecord' => 'Espo\\Entities\\ScheduledJobLogRecord',
  'Settings' => 'Espo\\Entities\\Settings',
  'Sms' => 'Espo\\Entities\\Sms',
  'StarSubscription' => 'Espo\\Entities\\StarSubscription',
  'StreamSubscription' => 'Espo\\Entities\\StreamSubscription',
  'SystemData' => 'Espo\\Entities\\SystemData',
  'Team' => 'Espo\\Entities\\Team',
  'Template' => 'Espo\\Entities\\Template',
  'TwoFactorCode' => 'Espo\\Entities\\TwoFactorCode',
  'UniqueId' => 'Espo\\Entities\\UniqueId',
  'User' => 'Espo\\Entities\\User',
  'UserData' => 'Espo\\Entities\\UserData',
  'UserReaction' => 'Espo\\Entities\\UserReaction',
  'Webhook' => 'Espo\\Entities\\Webhook',
  'WebhookEventQueueItem' => 'Espo\\Entities\\WebhookEventQueueItem',
  'WebhookQueueItem' => 'Espo\\Entities\\WebhookQueueItem',
  'WorkingTimeCalendar' => 'Espo\\Entities\\WorkingTimeCalendar',
  'WorkingTimeRange' => 'Espo\\Entities\\WorkingTimeRange',
  'Account' => 'Espo\\Modules\\Crm\\Entities\\Account',
  'Call' => 'Espo\\Modules\\Crm\\Entities\\Call',
  'Campaign' => 'Espo\\Modules\\Crm\\Entities\\Campaign',
  'CampaignLogRecord' => 'Espo\\Modules\\Crm\\Entities\\CampaignLogRecord',
  'CampaignTrackingUrl' => 'Espo\\Modules\\Crm\\Entities\\CampaignTrackingUrl',
  'Case' => 'Espo\\Modules\\Crm\\Entities\\CaseObj',
  'Contact' => 'Espo\\Modules\\Crm\\Entities\\Contact',
  'Document' => 'Espo\\Modules\\Crm\\Entities\\Document',
  'DocumentFolder' => 'Espo\\Modules\\Crm\\Entities\\DocumentFolder',
  'EmailQueueItem' => 'Espo\\Modules\\Crm\\Entities\\EmailQueueItem',
  'KnowledgeBaseArticle' => 'Espo\\Modules\\Crm\\Entities\\KnowledgeBaseArticle',
  'KnowledgeBaseCategory' => 'Espo\\Modules\\Crm\\Entities\\KnowledgeBaseCategory',
  'Lead' => 'Espo\\Modules\\Crm\\Entities\\Lead',
  'MassEmail' => 'Espo\\Modules\\Crm\\Entities\\MassEmail',
  'Meeting' => 'Espo\\Modules\\Crm\\Entities\\Meeting',
  'Opportunity' => 'Espo\\Modules\\Crm\\Entities\\Opportunity',
  'Reminder' => 'Espo\\Modules\\Crm\\Entities\\Reminder',
  'Target' => 'Espo\\Modules\\Crm\\Entities\\Target',
  'TargetList' => 'Espo\\Modules\\Crm\\Entities\\TargetList',
  'TargetListCategory' => 'Espo\\Modules\\Crm\\Entities\\TargetListCategory',
  'Task' => 'Espo\\Modules\\Crm\\Entities\\Task'
];
