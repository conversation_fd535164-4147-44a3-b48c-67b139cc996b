[2025-06-02 12:06:55] ERROR: Slim Application Error Type: Error Code: 0 Message: Call to a member function checkScope() on null File: C:\xampp\htdocs\espocrm_en\application\Espo\Modules\AutoCRM\Controllers\FindContacts.php Line: 30 Trace: #0 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\ControllerActionProcessor.php(87): Espo\Modules\AutoCRM\Controllers\FindContacts->postActionFindByEmail(Object(Espo\Core\Api\RequestWrapper), Object(Espo\Core\Api\ResponseWrapper)) #1 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\ControllerActionHandler.php(65): Espo\Core\Api\ControllerActionProcessor->process('FindContacts', 'findByEmail', Object(Espo\Core\Api\RequestWrapper), Object(Espo\Core\Api\ResponseWrapper)) #2 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Espo\Core\Api\ControllerActionHandler->handle(Object(Slim\Psr7\Request)) #3 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(221): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #4 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(148): Espo\Core\Api\RouteProcessor->processControllerAction(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Espo\Core\Api\ResponseWrapper)) #5 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(126): Espo\Core\Api\RouteProcessor->processAfterAuth(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Espo\Core\Api\ResponseWrapper)) #6 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(78): Espo\Core\Api\RouteProcessor->processInternal(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Espo\Core\Api\RequestWrapper), Object(Espo\Core\Api\ResponseWrapper)) #7 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\Starter.php(119): Espo\Core\Api\RouteProcessor->process(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Slim\Psr7\Response)) #8 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Handlers\Strategies\RequestResponse.php(38): Espo\Core\Api\Starter->Espo\Core\Api\{closure}(Object(Slim\Psr7\Request), Object(Slim\Psr7\Response), Array) #9 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Routing\Route.php(363): Slim\Handlers\Strategies\RequestResponse->__invoke(Object(Closure), Object(Slim\Psr7\Request), Object(Slim\Psr7\Response), Array) #10 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Slim\Routing\Route->handle(Object(Slim\Psr7\Request)) #11 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #12 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Routing\Route.php(321): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #13 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Routing\RouteRunner.php(74): Slim\Routing\Route->run(Object(Slim\Psr7\Request)) #14 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Middleware\RoutingMiddleware.php(45): Slim\Routing\RouteRunner->handle(Object(Slim\Psr7\Request)) #15 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(129): Slim\Middleware\RoutingMiddleware->process(Object(Slim\Psr7\Request), Object(Slim\Routing\RouteRunner)) #16 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Middleware\ErrorMiddleware.php(77): Psr\Http\Server\RequestHandlerInterface@anonymous->handle(Object(Slim\Psr7\Request)) #17 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(129): Slim\Middleware\ErrorMiddleware->process(Object(Slim\Psr7\Request), Object(Psr\Http\Server\RequestHandlerInterface@anonymous)) #18 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Psr\Http\Server\RequestHandlerInterface@anonymous->handle(Object(Slim\Psr7\Request)) #19 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\App.php(209): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #20 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\App.php(193): Slim\App->handle(Object(Slim\Psr7\Request)) #21 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\Starter.php(77): Slim\App->run() #22 C:\xampp\htdocs\espocrm_en\application\Espo\Core\ApplicationRunners\Api.php(45): Espo\Core\Api\Starter->start() #23 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Application\RunnerRunner.php(84): Espo\Core\ApplicationRunners\Api->run() #24 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Application.php(78): Espo\Core\Application\RunnerRunner->run('Espo\\Core\\Appli...', NULL) #25 C:\xampp\htdocs\espocrm_en\public\api\v1\index.php(35): Espo\Core\Application->run('Espo\\Core\\Appli...') #26 {main} Tips: To display error details in HTTP response set "displayErrorDetails" to true in the ErrorHandler constructor.
[2025-06-02 12:06:56] ERROR: Slim Application Error Type: Error Code: 0 Message: Call to a member function checkScope() on null File: C:\xampp\htdocs\espocrm_en\application\Espo\Modules\AutoCRM\Controllers\FindContacts.php Line: 30 Trace: #0 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\ControllerActionProcessor.php(87): Espo\Modules\AutoCRM\Controllers\FindContacts->postActionFindByEmail(Object(Espo\Core\Api\RequestWrapper), Object(Espo\Core\Api\ResponseWrapper)) #1 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\ControllerActionHandler.php(65): Espo\Core\Api\ControllerActionProcessor->process('FindContacts', 'findByEmail', Object(Espo\Core\Api\RequestWrapper), Object(Espo\Core\Api\ResponseWrapper)) #2 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Espo\Core\Api\ControllerActionHandler->handle(Object(Slim\Psr7\Request)) #3 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(221): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #4 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(148): Espo\Core\Api\RouteProcessor->processControllerAction(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Espo\Core\Api\ResponseWrapper)) #5 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(126): Espo\Core\Api\RouteProcessor->processAfterAuth(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Espo\Core\Api\ResponseWrapper)) #6 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(78): Espo\Core\Api\RouteProcessor->processInternal(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Espo\Core\Api\RequestWrapper), Object(Espo\Core\Api\ResponseWrapper)) #7 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\Starter.php(119): Espo\Core\Api\RouteProcessor->process(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Slim\Psr7\Response)) #8 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Handlers\Strategies\RequestResponse.php(38): Espo\Core\Api\Starter->Espo\Core\Api\{closure}(Object(Slim\Psr7\Request), Object(Slim\Psr7\Response), Array) #9 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Routing\Route.php(363): Slim\Handlers\Strategies\RequestResponse->__invoke(Object(Closure), Object(Slim\Psr7\Request), Object(Slim\Psr7\Response), Array) #10 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Slim\Routing\Route->handle(Object(Slim\Psr7\Request)) #11 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #12 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Routing\Route.php(321): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #13 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Routing\RouteRunner.php(74): Slim\Routing\Route->run(Object(Slim\Psr7\Request)) #14 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Middleware\RoutingMiddleware.php(45): Slim\Routing\RouteRunner->handle(Object(Slim\Psr7\Request)) #15 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(129): Slim\Middleware\RoutingMiddleware->process(Object(Slim\Psr7\Request), Object(Slim\Routing\RouteRunner)) #16 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Middleware\ErrorMiddleware.php(77): Psr\Http\Server\RequestHandlerInterface@anonymous->handle(Object(Slim\Psr7\Request)) #17 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(129): Slim\Middleware\ErrorMiddleware->process(Object(Slim\Psr7\Request), Object(Psr\Http\Server\RequestHandlerInterface@anonymous)) #18 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Psr\Http\Server\RequestHandlerInterface@anonymous->handle(Object(Slim\Psr7\Request)) #19 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\App.php(209): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #20 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\App.php(193): Slim\App->handle(Object(Slim\Psr7\Request)) #21 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\Starter.php(77): Slim\App->run() #22 C:\xampp\htdocs\espocrm_en\application\Espo\Core\ApplicationRunners\Api.php(45): Espo\Core\Api\Starter->start() #23 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Application\RunnerRunner.php(84): Espo\Core\ApplicationRunners\Api->run() #24 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Application.php(78): Espo\Core\Application\RunnerRunner->run('Espo\\Core\\Appli...', NULL) #25 C:\xampp\htdocs\espocrm_en\public\api\v1\index.php(35): Espo\Core\Application->run('Espo\\Core\\Appli...') #26 {main} Tips: To display error details in HTTP response set "displayErrorDetails" to true in the ErrorHandler constructor.
[2025-06-02 12:06:57] ERROR: Slim Application Error Type: Error Code: 0 Message: Call to a member function checkScope() on null File: C:\xampp\htdocs\espocrm_en\application\Espo\Modules\AutoCRM\Controllers\FindContacts.php Line: 30 Trace: #0 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\ControllerActionProcessor.php(87): Espo\Modules\AutoCRM\Controllers\FindContacts->postActionFindByEmail(Object(Espo\Core\Api\RequestWrapper), Object(Espo\Core\Api\ResponseWrapper)) #1 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\ControllerActionHandler.php(65): Espo\Core\Api\ControllerActionProcessor->process('FindContacts', 'findByEmail', Object(Espo\Core\Api\RequestWrapper), Object(Espo\Core\Api\ResponseWrapper)) #2 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Espo\Core\Api\ControllerActionHandler->handle(Object(Slim\Psr7\Request)) #3 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(221): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #4 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(148): Espo\Core\Api\RouteProcessor->processControllerAction(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Espo\Core\Api\ResponseWrapper)) #5 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(126): Espo\Core\Api\RouteProcessor->processAfterAuth(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Espo\Core\Api\ResponseWrapper)) #6 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(78): Espo\Core\Api\RouteProcessor->processInternal(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Espo\Core\Api\RequestWrapper), Object(Espo\Core\Api\ResponseWrapper)) #7 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\Starter.php(119): Espo\Core\Api\RouteProcessor->process(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Slim\Psr7\Response)) #8 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Handlers\Strategies\RequestResponse.php(38): Espo\Core\Api\Starter->Espo\Core\Api\{closure}(Object(Slim\Psr7\Request), Object(Slim\Psr7\Response), Array) #9 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Routing\Route.php(363): Slim\Handlers\Strategies\RequestResponse->__invoke(Object(Closure), Object(Slim\Psr7\Request), Object(Slim\Psr7\Response), Array) #10 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Slim\Routing\Route->handle(Object(Slim\Psr7\Request)) #11 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #12 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Routing\Route.php(321): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #13 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Routing\RouteRunner.php(74): Slim\Routing\Route->run(Object(Slim\Psr7\Request)) #14 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Middleware\RoutingMiddleware.php(45): Slim\Routing\RouteRunner->handle(Object(Slim\Psr7\Request)) #15 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(129): Slim\Middleware\RoutingMiddleware->process(Object(Slim\Psr7\Request), Object(Slim\Routing\RouteRunner)) #16 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Middleware\ErrorMiddleware.php(77): Psr\Http\Server\RequestHandlerInterface@anonymous->handle(Object(Slim\Psr7\Request)) #17 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(129): Slim\Middleware\ErrorMiddleware->process(Object(Slim\Psr7\Request), Object(Psr\Http\Server\RequestHandlerInterface@anonymous)) #18 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Psr\Http\Server\RequestHandlerInterface@anonymous->handle(Object(Slim\Psr7\Request)) #19 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\App.php(209): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #20 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\App.php(193): Slim\App->handle(Object(Slim\Psr7\Request)) #21 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\Starter.php(77): Slim\App->run() #22 C:\xampp\htdocs\espocrm_en\application\Espo\Core\ApplicationRunners\Api.php(45): Espo\Core\Api\Starter->start() #23 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Application\RunnerRunner.php(84): Espo\Core\ApplicationRunners\Api->run() #24 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Application.php(78): Espo\Core\Application\RunnerRunner->run('Espo\\Core\\Appli...', NULL) #25 C:\xampp\htdocs\espocrm_en\public\api\v1\index.php(35): Espo\Core\Application->run('Espo\\Core\\Appli...') #26 {main} Tips: To display error details in HTTP response set "displayErrorDetails" to true in the ErrorHandler constructor.
[2025-06-02 12:07:00] ERROR: Slim Application Error Type: Error Code: 0 Message: Call to a member function checkScope() on null File: C:\xampp\htdocs\espocrm_en\application\Espo\Modules\AutoCRM\Controllers\FindContacts.php Line: 30 Trace: #0 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\ControllerActionProcessor.php(87): Espo\Modules\AutoCRM\Controllers\FindContacts->postActionFindByEmail(Object(Espo\Core\Api\RequestWrapper), Object(Espo\Core\Api\ResponseWrapper)) #1 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\ControllerActionHandler.php(65): Espo\Core\Api\ControllerActionProcessor->process('FindContacts', 'findByEmail', Object(Espo\Core\Api\RequestWrapper), Object(Espo\Core\Api\ResponseWrapper)) #2 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Espo\Core\Api\ControllerActionHandler->handle(Object(Slim\Psr7\Request)) #3 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(221): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #4 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(148): Espo\Core\Api\RouteProcessor->processControllerAction(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Espo\Core\Api\ResponseWrapper)) #5 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(126): Espo\Core\Api\RouteProcessor->processAfterAuth(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Espo\Core\Api\ResponseWrapper)) #6 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(78): Espo\Core\Api\RouteProcessor->processInternal(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Espo\Core\Api\RequestWrapper), Object(Espo\Core\Api\ResponseWrapper)) #7 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\Starter.php(119): Espo\Core\Api\RouteProcessor->process(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Slim\Psr7\Response)) #8 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Handlers\Strategies\RequestResponse.php(38): Espo\Core\Api\Starter->Espo\Core\Api\{closure}(Object(Slim\Psr7\Request), Object(Slim\Psr7\Response), Array) #9 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Routing\Route.php(363): Slim\Handlers\Strategies\RequestResponse->__invoke(Object(Closure), Object(Slim\Psr7\Request), Object(Slim\Psr7\Response), Array) #10 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Slim\Routing\Route->handle(Object(Slim\Psr7\Request)) #11 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #12 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Routing\Route.php(321): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #13 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Routing\RouteRunner.php(74): Slim\Routing\Route->run(Object(Slim\Psr7\Request)) #14 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Middleware\RoutingMiddleware.php(45): Slim\Routing\RouteRunner->handle(Object(Slim\Psr7\Request)) #15 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(129): Slim\Middleware\RoutingMiddleware->process(Object(Slim\Psr7\Request), Object(Slim\Routing\RouteRunner)) #16 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Middleware\ErrorMiddleware.php(77): Psr\Http\Server\RequestHandlerInterface@anonymous->handle(Object(Slim\Psr7\Request)) #17 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(129): Slim\Middleware\ErrorMiddleware->process(Object(Slim\Psr7\Request), Object(Psr\Http\Server\RequestHandlerInterface@anonymous)) #18 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Psr\Http\Server\RequestHandlerInterface@anonymous->handle(Object(Slim\Psr7\Request)) #19 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\App.php(209): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #20 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\App.php(193): Slim\App->handle(Object(Slim\Psr7\Request)) #21 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\Starter.php(77): Slim\App->run() #22 C:\xampp\htdocs\espocrm_en\application\Espo\Core\ApplicationRunners\Api.php(45): Espo\Core\Api\Starter->start() #23 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Application\RunnerRunner.php(84): Espo\Core\ApplicationRunners\Api->run() #24 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Application.php(78): Espo\Core\Application\RunnerRunner->run('Espo\\Core\\Appli...', NULL) #25 C:\xampp\htdocs\espocrm_en\public\api\v1\index.php(35): Espo\Core\Application->run('Espo\\Core\\Appli...') #26 {main} Tips: To display error details in HTTP response set "displayErrorDetails" to true in the ErrorHandler constructor.
[2025-06-02 12:07:03] ERROR: Slim Application Error Type: Error Code: 0 Message: Call to a member function checkScope() on null File: C:\xampp\htdocs\espocrm_en\application\Espo\Modules\AutoCRM\Controllers\FindContacts.php Line: 30 Trace: #0 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\ControllerActionProcessor.php(87): Espo\Modules\AutoCRM\Controllers\FindContacts->postActionFindByEmail(Object(Espo\Core\Api\RequestWrapper), Object(Espo\Core\Api\ResponseWrapper)) #1 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\ControllerActionHandler.php(65): Espo\Core\Api\ControllerActionProcessor->process('FindContacts', 'findByEmail', Object(Espo\Core\Api\RequestWrapper), Object(Espo\Core\Api\ResponseWrapper)) #2 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Espo\Core\Api\ControllerActionHandler->handle(Object(Slim\Psr7\Request)) #3 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(221): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #4 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(148): Espo\Core\Api\RouteProcessor->processControllerAction(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Espo\Core\Api\ResponseWrapper)) #5 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(126): Espo\Core\Api\RouteProcessor->processAfterAuth(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Espo\Core\Api\ResponseWrapper)) #6 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(78): Espo\Core\Api\RouteProcessor->processInternal(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Espo\Core\Api\RequestWrapper), Object(Espo\Core\Api\ResponseWrapper)) #7 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\Starter.php(119): Espo\Core\Api\RouteProcessor->process(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Slim\Psr7\Response)) #8 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Handlers\Strategies\RequestResponse.php(38): Espo\Core\Api\Starter->Espo\Core\Api\{closure}(Object(Slim\Psr7\Request), Object(Slim\Psr7\Response), Array) #9 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Routing\Route.php(363): Slim\Handlers\Strategies\RequestResponse->__invoke(Object(Closure), Object(Slim\Psr7\Request), Object(Slim\Psr7\Response), Array) #10 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Slim\Routing\Route->handle(Object(Slim\Psr7\Request)) #11 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #12 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Routing\Route.php(321): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #13 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Routing\RouteRunner.php(74): Slim\Routing\Route->run(Object(Slim\Psr7\Request)) #14 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Middleware\RoutingMiddleware.php(45): Slim\Routing\RouteRunner->handle(Object(Slim\Psr7\Request)) #15 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(129): Slim\Middleware\RoutingMiddleware->process(Object(Slim\Psr7\Request), Object(Slim\Routing\RouteRunner)) #16 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Middleware\ErrorMiddleware.php(77): Psr\Http\Server\RequestHandlerInterface@anonymous->handle(Object(Slim\Psr7\Request)) #17 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(129): Slim\Middleware\ErrorMiddleware->process(Object(Slim\Psr7\Request), Object(Psr\Http\Server\RequestHandlerInterface@anonymous)) #18 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Psr\Http\Server\RequestHandlerInterface@anonymous->handle(Object(Slim\Psr7\Request)) #19 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\App.php(209): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #20 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\App.php(193): Slim\App->handle(Object(Slim\Psr7\Request)) #21 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\Starter.php(77): Slim\App->run() #22 C:\xampp\htdocs\espocrm_en\application\Espo\Core\ApplicationRunners\Api.php(45): Espo\Core\Api\Starter->start() #23 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Application\RunnerRunner.php(84): Espo\Core\ApplicationRunners\Api->run() #24 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Application.php(78): Espo\Core\Application\RunnerRunner->run('Espo\\Core\\Appli...', NULL) #25 C:\xampp\htdocs\espocrm_en\public\api\v1\index.php(35): Espo\Core\Application->run('Espo\\Core\\Appli...') #26 {main} Tips: To display error details in HTTP response set "displayErrorDetails" to true in the ErrorHandler constructor.
[2025-06-02 12:07:06] ERROR: Slim Application Error Type: Error Code: 0 Message: Call to a member function checkScope() on null File: C:\xampp\htdocs\espocrm_en\application\Espo\Modules\AutoCRM\Controllers\FindContacts.php Line: 30 Trace: #0 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\ControllerActionProcessor.php(87): Espo\Modules\AutoCRM\Controllers\FindContacts->postActionFindByEmail(Object(Espo\Core\Api\RequestWrapper), Object(Espo\Core\Api\ResponseWrapper)) #1 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\ControllerActionHandler.php(65): Espo\Core\Api\ControllerActionProcessor->process('FindContacts', 'findByEmail', Object(Espo\Core\Api\RequestWrapper), Object(Espo\Core\Api\ResponseWrapper)) #2 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Espo\Core\Api\ControllerActionHandler->handle(Object(Slim\Psr7\Request)) #3 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(221): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #4 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(148): Espo\Core\Api\RouteProcessor->processControllerAction(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Espo\Core\Api\ResponseWrapper)) #5 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(126): Espo\Core\Api\RouteProcessor->processAfterAuth(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Espo\Core\Api\ResponseWrapper)) #6 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\RouteProcessor.php(78): Espo\Core\Api\RouteProcessor->processInternal(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Espo\Core\Api\RequestWrapper), Object(Espo\Core\Api\ResponseWrapper)) #7 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\Starter.php(119): Espo\Core\Api\RouteProcessor->process(Object(Espo\Core\Api\ProcessData), Object(Slim\Psr7\Request), Object(Slim\Psr7\Response)) #8 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Handlers\Strategies\RequestResponse.php(38): Espo\Core\Api\Starter->Espo\Core\Api\{closure}(Object(Slim\Psr7\Request), Object(Slim\Psr7\Response), Array) #9 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Routing\Route.php(363): Slim\Handlers\Strategies\RequestResponse->__invoke(Object(Closure), Object(Slim\Psr7\Request), Object(Slim\Psr7\Response), Array) #10 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Slim\Routing\Route->handle(Object(Slim\Psr7\Request)) #11 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #12 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Routing\Route.php(321): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #13 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Routing\RouteRunner.php(74): Slim\Routing\Route->run(Object(Slim\Psr7\Request)) #14 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Middleware\RoutingMiddleware.php(45): Slim\Routing\RouteRunner->handle(Object(Slim\Psr7\Request)) #15 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(129): Slim\Middleware\RoutingMiddleware->process(Object(Slim\Psr7\Request), Object(Slim\Routing\RouteRunner)) #16 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\Middleware\ErrorMiddleware.php(77): Psr\Http\Server\RequestHandlerInterface@anonymous->handle(Object(Slim\Psr7\Request)) #17 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(129): Slim\Middleware\ErrorMiddleware->process(Object(Slim\Psr7\Request), Object(Psr\Http\Server\RequestHandlerInterface@anonymous)) #18 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\MiddlewareDispatcher.php(73): Psr\Http\Server\RequestHandlerInterface@anonymous->handle(Object(Slim\Psr7\Request)) #19 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\App.php(209): Slim\MiddlewareDispatcher->handle(Object(Slim\Psr7\Request)) #20 C:\xampp\htdocs\espocrm_en\vendor\slim\slim\Slim\App.php(193): Slim\App->handle(Object(Slim\Psr7\Request)) #21 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Api\Starter.php(77): Slim\App->run() #22 C:\xampp\htdocs\espocrm_en\application\Espo\Core\ApplicationRunners\Api.php(45): Espo\Core\Api\Starter->start() #23 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Application\RunnerRunner.php(84): Espo\Core\ApplicationRunners\Api->run() #24 C:\xampp\htdocs\espocrm_en\application\Espo\Core\Application.php(78): Espo\Core\Application\RunnerRunner->run('Espo\\Core\\Appli...', NULL) #25 C:\xampp\htdocs\espocrm_en\public\api\v1\index.php(35): Espo\Core\Application->run('Espo\\Core\\Appli...') #26 {main} Tips: To display error details in HTTP response set "displayErrorDetails" to true in the ErrorHandler constructor.
[2025-06-02 12:17:45] WARNING: E_WARNING: rename(data\tmp\tmp8CCD.tmp,data/config.php): Access is denied (code: 5)
[2025-06-02 12:33:03] WARNING: E_WARNING: rename(data\tmp\tmp9179.tmp,data/cache/application/classmapControllers.php): Access is denied (code: 5)
