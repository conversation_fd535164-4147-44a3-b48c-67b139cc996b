<?php
/************************************************************************
 * This file is part of AutoCRM extension.
 *
 * AutoCRM is free software: you can redistribute it and/or modify
 * it under the terms of the MIT License.
 *
 * @package   AutoCRM
 * <AUTHOR> Development Team
 * @copyright 2025 AutoCRM
 * @license   MIT
 ************************************************************************/

declare(strict_types=1);

namespace Espo\Modules\AutoCRM\Hooks\Account;

use Espo\Modules\AutoCRM\Core\Hooks\BaseNamingHook;

/**
 * Account Apertia Naming Hook
 *
 * Automatically appends Apertia suffix to Account entity names
 * following professional naming conventions and business rules.
 */
class ApertiaNameHook extends BaseNamingHook
{
    /** @var int Hook execution order */
    public static $order = 5;

    /**
     * Get the entity type this hook handles
     */
    protected function getEntityType(): string
    {
        return 'Account';
    }

    /**
     * Get hook execution order from configuration
     */
    public static function getOrder(): int
    {
        return parent::getOrder();
    }
}
