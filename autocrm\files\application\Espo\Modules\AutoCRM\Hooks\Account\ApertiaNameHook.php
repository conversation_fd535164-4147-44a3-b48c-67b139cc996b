<?php

namespace Espo\Modules\AutoCRM\Hooks\Account;

use Espo\ORM\Entity;
use Espo\Core\Hook\Hook\BeforeSave;

/**
 * Account hook to automatically append "- Apertia" to account names
 */
class ApertiaNameHook implements BeforeSave
{
    /**
     * Hook execution order
     */
    public static int $order = 10;

    /**
     * Before save hook - appends "- Apertia" to account name
     *
     * @param Entity $entity The Account entity being saved
     * @param array $options Save options
     */
    public function beforeSave(Entity $entity, array $options): void
    {
        // Only process Account entities
        if ($entity->getEntityType() !== 'Account') {
            return;
        }

        $name = $entity->get('name');
        
        // Handle empty or null names
        if (empty($name)) {
            $entity->set('name', 'Ukázka - Apertia');
            return;
        }

        // Check if "- Apertia" is already present
        if (!$this->hasApertiaSuffix($name)) {
            $entity->set('name', $name . ' - Apertia');
        }
    }

    /**
     * Check if the name already ends with "- <PERSON>pertia"
     *
     * @param string $name The name to check
     * @return bool True if suffix is already present
     */
    private function hasApertiaSuffix(string $name): bool
    {
        $suffix = ' - Apertia';
        return str_ends_with($name, $suffix);
    }
}
