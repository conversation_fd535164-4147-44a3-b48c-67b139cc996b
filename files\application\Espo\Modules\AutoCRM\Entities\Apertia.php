<?php
/************************************************************************
 * This file is part of AutoCRM extension.
 * 
 * @package   AutoCRM
 * <AUTHOR> Development Team
 * @copyright 2025 AutoCRM
 * @license   MIT
 ************************************************************************/

declare(strict_types=1);

namespace Espo\Modules\AutoCRM\Entities;

use Espo\Core\ORM\Entity;

/**
 * Apertia Entity
 * 
 * Simple, reliable entity implementation with essential functionality.
 */
class Apertia extends Entity
{
    public const ENTITY_TYPE = 'Apertia';
    
    // Status constants
    public const STATUS_DRAFT = 'Draft';
    public const STATUS_ACTIVE = 'Active';
    public const STATUS_COMPLETED = 'Completed';
    public const STATUS_CANCELED = 'Canceled';

    /**
     * Get entity name
     */
    public function getName(): ?string
    {
        return $this->get('name');
    }

    /**
     * Set entity name
     */
    public function setName(?string $name): self
    {
        $this->set('name', $name);
        return $this;
    }

    /**
     * Get status
     */
    public function getStatus(): ?string
    {
        return $this->get('status');
    }

    /**
     * Set status
     */
    public function setStatus(?string $status): self
    {
        $this->set('status', $status);
        return $this;
    }

    /**
     * Get assigned user ID
     */
    public function getAssignedUserId(): ?string
    {
        return $this->get('assignedUserId');
    }

    /**
     * Set assigned user ID
     */
    public function setAssignedUserId(?string $assignedUserId): self
    {
        $this->set('assignedUserId', $assignedUserId);
        return $this;
    }

    /**
     * Get description
     */
    public function getDescription(): ?string
    {
        return $this->get('description');
    }

    /**
     * Set description
     */
    public function setDescription(?string $description): self
    {
        $this->set('description', $description);
        return $this;
    }

    /**
     * Check if entity is in draft status
     */
    public function isDraft(): bool
    {
        return $this->getStatus() === self::STATUS_DRAFT;
    }

    /**
     * Check if entity is active
     */
    public function isActive(): bool
    {
        return $this->getStatus() === self::STATUS_ACTIVE;
    }

    /**
     * Check if entity is completed
     */
    public function isCompleted(): bool
    {
        return $this->getStatus() === self::STATUS_COMPLETED;
    }

    /**
     * Check if entity is canceled
     */
    public function isCanceled(): bool
    {
        return $this->getStatus() === self::STATUS_CANCELED;
    }
}
