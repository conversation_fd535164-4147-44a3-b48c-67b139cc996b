{"name": "AutoCRM", "version": "2.0.0", "author": "AutoCRM Development Team", "description": "Professional CRM extension providing advanced contact management, custom Apertia entity with automated naming conventions, and enhanced Lead functionality for streamlined business operations.", "acceptableVersions": [">=8.0.0"], "skipBackup": false, "releaseDate": "2025-01-15", "license": "MIT", "homepage": "https://github.com/autocrm/espocrm-extension", "support": {"email": "<EMAIL>", "documentation": "https://docs.autocrm.com"}, "keywords": ["crm", "automation", "contacts", "leads", "apertia"], "requirements": {"php": ">=8.1", "espocrm": ">=8.0.0"}, "changelog": {"2.0.0": ["Enhanced error handling and logging", "Improved code quality and documentation", "Added configuration management", "Professional-grade architecture", "Enhanced security and performance"]}}