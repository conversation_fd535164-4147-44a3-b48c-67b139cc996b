<?php
/************************************************************************
 * This file is part of AutoCRM extension.
 * 
 * @package   AutoCRM
 * <AUTHOR> Development Team
 * @copyright 2025 AutoCRM
 * @license   MIT
 ************************************************************************/

declare(strict_types=1);

namespace Espo\Custom;

use Espo\Core\Utils\Config;
use Espo\Core\Utils\Config\ConfigWriter;

/**
 * AutoCRM Setup Manager
 * 
 * Simple, reliable installation and uninstallation with minimal dependencies.
 * Based on the proven approach from AutoCRM_poslane.zip but with modern improvements.
 */
class Setup
{
    private Config $config;
    private ConfigWriter $configWriter;

    public function __construct(Config $config, ConfigWriter $configWriter)
    {
        $this->config = $config;
        $this->configWriter = $configWriter;
    }

    /**
     * Called after extension installation
     */
    public function afterInstall(): void
    {
        $this->addApertiaToNavigation();
    }

    /**
     * Called after extension upgrade
     */
    public function afterUpgrade(): void
    {
        $this->addApertiaToNavigation();
    }

    /**
     * Called after extension uninstallation
     */
    public function afterUninstall(): void
    {
        $this->removeApertiaFromNavigation();
    }

    /**
     * Add Apertia entity to navigation menu
     * Uses the proven approach from AutoCRM_poslane.zip
     */
    private function addApertiaToNavigation(): void
    {
        try {
            $tabList = $this->config->get('tabList', []);

            // Add Apertia to navigation if not already present
            if (!in_array('Apertia', $tabList, true)) {
                $tabList[] = 'Apertia';
                $this->configWriter->set('tabList', $tabList);
                $this->configWriter->save();
            }
        } catch (\Throwable $e) {
            // Silent fail - don't break installation
            // This ensures the extension installs even if navigation setup fails
            error_log('AutoCRM Setup: Navigation setup failed - ' . $e->getMessage());
        }
    }

    /**
     * Remove Apertia entity from navigation menu
     */
    private function removeApertiaFromNavigation(): void
    {
        try {
            $tabList = $this->config->get('tabList', []);

            // Remove Apertia from navigation if present
            $apertiaIndex = array_search('Apertia', $tabList, true);
            if ($apertiaIndex !== false) {
                array_splice($tabList, $apertiaIndex, 1);
                $this->configWriter->set('tabList', $tabList);
                $this->configWriter->save();
            }
        } catch (\Throwable $e) {
            // Silent fail - don't break uninstallation
            error_log('AutoCRM Setup: Navigation cleanup failed - ' . $e->getMessage());
        }
    }
}
