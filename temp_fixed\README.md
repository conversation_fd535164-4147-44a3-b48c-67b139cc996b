# AutoCRM Extension for EspoCRM

[![Version](https://img.shields.io/badge/version-2.0.0-blue.svg)](https://github.com/autocrm/espocrm-extension)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![EspoCRM](https://img.shields.io/badge/EspoCRM-8.0%2B-orange.svg)](https://www.espocrm.com/)
[![PHP](https://img.shields.io/badge/PHP-8.1%2B-purple.svg)](https://php.net/)

Professional CRM extension providing advanced contact management, custom Apertia entity with automated naming conventions, and enhanced Lead functionality for streamlined business operations.

## 🚀 Features

### Core Functionality
- **Custom Apertia Entity**: Complete CRUD operations with professional UI
- **Automated Naming**: Intelligent suffix appending for Account and Apertia entities
- **Enhanced Lead Management**: Advanced contact search and management tools
- **Professional Architecture**: Enterprise-grade code quality and structure

### Technical Excellence
- **Type-Safe Configuration**: Centralized configuration management with validation
- **Comprehensive Logging**: Structured logging with performance monitoring
- **Error Handling**: Robust exception handling and recovery mechanisms
- **Extensible Design**: Clean architecture for easy customization and extension

## 📋 Requirements

- **EspoCRM**: 8.0 or higher
- **PHP**: 8.1 or higher
- **Extensions**: json, mbstring, openssl, pdo
- **Memory**: 128MB minimum (256MB recommended)

## 🔧 Installation

### Method 1: Extension Manager (Recommended)
1. Download the latest `AutoCRM.zip` from releases
2. Navigate to **Administration > Extensions**
3. Click **Upload** and select the downloaded file
4. Click **Install** and wait for completion
5. Clear cache: **Administration > Clear Cache**

### Method 2: Manual Installation
```bash
# Extract to EspoCRM root directory
unzip AutoCRM.zip -d /path/to/espocrm/

# Set proper permissions
chmod -R 755 application/Espo/Modules/AutoCRM/
chmod -R 755 client/custom/modules/autocrm/

# Clear cache
php rebuild.php
```

## ⚙️ Configuration

### Basic Configuration
The extension works out-of-the-box with sensible defaults:

```php
// Default configuration
$config = [
    'apertia_suffix' => ' - Apertia',
    'empty_name' => 'Ukázka - Apertia',
    'hook_order' => 5,
    'enabled_entities' => ['Account', 'Apertia']
];
```

### Advanced Configuration
For custom configurations, modify the AutoCRMConfig class:

```php
// Custom suffix
$config = new AutoCRMConfig([
    'apertia_suffix' => ' - Custom Suffix',
    'enabled_entities' => ['Account'] // Only enable for Accounts
]);
```

## 📖 Usage

### Apertia Entity Management
1. Navigate to **Apertia** in the main menu
2. Click **Create Apertia** to add new records
3. Names are automatically suffixed with " - Apertia"
4. Use filters to search and organize records

### Account Enhancement
- All Account names automatically receive the Apertia suffix
- Empty names are set to "Ukázka - Apertia"
- Existing suffixes are preserved (no duplicates)

### Lead Contact Search
1. Open any Lead record
2. Use the **Find Contacts** button
3. Search by email address
4. View matching contacts with detailed information

## 🏗️ Architecture

### Directory Structure
```
application/Espo/Modules/AutoCRM/
├── Core/
│   ├── Config/AutoCRMConfig.php      # Configuration management
│   ├── Hooks/BaseNamingHook.php      # Base hook functionality
│   └── Utils/Logger.php              # Enhanced logging
├── Controllers/
│   ├── Apertia.php                   # Apertia CRUD controller
│   └── FindContacts.php              # Contact search API
├── Entities/Apertia.php              # Apertia ORM entity
├── Hooks/
│   ├── Account/ApertiaNameHook.php   # Account naming hook
│   └── Apertia/ApertiaNameHook.php   # Apertia naming hook
├── Repositories/Apertia.php          # Database operations
├── Resources/                        # Metadata and layouts
└── Setup.php                         # Installation manager
```

### Key Components

#### Configuration Management
- **AutoCRMConfig**: Type-safe configuration with validation
- **Centralized Settings**: Single source of truth for all options
- **Runtime Validation**: Ensures configuration integrity

#### Logging System
- **Structured Logging**: Contextual information for debugging
- **Performance Monitoring**: Execution time tracking
- **Exception Handling**: Comprehensive error logging

#### Hook Architecture
- **BaseNamingHook**: Abstract base class for naming logic
- **Entity-Specific Hooks**: Specialized implementations
- **Error Recovery**: Graceful handling of hook failures

## 🧪 Testing

### Manual Testing
1. Create new Account records and verify naming
2. Create new Apertia records and verify functionality
3. Test Lead contact search functionality
4. Verify navigation integration

### Automated Testing
```bash
# Run PHPUnit tests (if available)
vendor/bin/phpunit tests/

# Check code quality
vendor/bin/phpstan analyse application/Espo/Modules/AutoCRM/
```

## 🔍 Troubleshooting

### Common Issues

#### Installation Fails
- Check PHP version (8.1+ required)
- Verify file permissions
- Check EspoCRM logs: `data/logs/espo.log`

#### Hooks Not Working
- Clear cache: Administration > Clear Cache
- Check entity configuration in Entity Manager
- Review AutoCRM logs for errors

#### Navigation Missing
- Manually add via Administration > User Interface
- Check Setup.php execution logs
- Verify tabList configuration

### Debug Mode
Enable detailed logging by modifying the Logger configuration:

```php
$logger = new Logger($log, 'AutoCRM');
$logger->debug('Debug information', $context);
```

## 🤝 Contributing

We welcome contributions! Please follow these guidelines:

1. **Fork** the repository
2. **Create** a feature branch: `git checkout -b feature/amazing-feature`
3. **Commit** changes: `git commit -m 'Add amazing feature'`
4. **Push** to branch: `git push origin feature/amazing-feature`
5. **Open** a Pull Request

### Development Standards
- Follow PSR-12 coding standards
- Add comprehensive documentation
- Include unit tests for new features
- Maintain backward compatibility

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [https://docs.autocrm.com](https://docs.autocrm.com)
- **Issues**: [GitHub Issues](https://github.com/autocrm/espocrm-extension/issues)
- **Email**: <EMAIL>
- **Community**: [EspoCRM Forum](https://forum.espocrm.com/)

## 📈 Changelog

### Version 2.0.0 (2025-01-15)
- ✨ Enhanced error handling and logging
- 🏗️ Improved code quality and documentation
- ⚙️ Added configuration management
- 🔧 Professional-grade architecture
- 🚀 Enhanced security and performance
- 📚 Comprehensive documentation

### Version 1.0.0 (2024-01-15)
- 🎉 Initial release
- 📝 Basic Apertia entity functionality
- 🔗 Account naming hooks
- 🔍 Lead contact search

## 🙏 Acknowledgments

- EspoCRM team for the excellent CRM platform
- Community contributors and testers
- Open source libraries and tools used

---

**Made with ❤️ by the AutoCRM Development Team**
