# AutoCRM Extension - Find Contacts Feature

## Overview
This extension adds a "Find Contacts" button to the Lead detail view in EspoCRM. When clicked, it searches for contacts with the same email address as the lead and displays the results in an alert dialog. It also provides a quick way to navigate to the Contacts list with the email pre-filled in the search.

## Features
- **Find Contacts Button**: Added to Lead detail view
- **Email Search**: Searches for contacts with the same email address as the current lead
- **Results Display**: Shows count and names of matching contacts in an alert dialog
- **Quick Navigation**: Option to navigate directly to Contacts list with email filter applied
- **Multilingual Support**: Includes English and Czech translations
- **Professional Implementation**: Clean, maintainable code following EspoCRM best practices

## Installation

1. **Download**: Ensure you have the `AutoCRM.zip` file
2. **Upload Extension**:
   - Go to Administration → Extensions
   - Click "Upload" button
   - Select the `AutoCRM.zip` file
   - Click "Install"
3. **Clear Cache**: The extension will automatically clear cache after installation
4. **Rebuild**: Go to Administration → Rebuild to ensure all changes are applied

## Usage

1. **Navigate to Lead**: Go to any Lead detail view
2. **Find Button**: Look for the "Find Contacts" button in the button bar
3. **Click Button**: Click the "Find Contacts" button
4. **View Results**: 
   - If contacts are found, you'll see an alert with count and names
   - If no contacts are found, you'll see a "no results" message
5. **Navigate to Contacts**: Click "Go to Contacts" to view the filtered contact list

## Technical Details

### Files Structure
```
AutoCRM/
├── manifest.json                                    # Extension manifest
├── files/
│   ├── application/Espo/Modules/AutoCRM/
│   │   ├── Controllers/FindContacts.php            # Backend API controller
│   │   ├── Setup.php                               # Installation setup
│   │   └── Resources/
│   │       ├── routes.json                         # API routes
│   │       ├── metadata/clientDefs/Lead.json       # Lead entity configuration
│   │       └── i18n/                               # Translations
│   │           ├── en_US/AutoCRM.json              # English translations
│   │           └── cs_CZ/AutoCRM.json              # Czech translations
│   └── client/custom/modules/autocrm/src/views/lead/record/
│       └── detail.js                               # Frontend Lead detail view extension
```

### API Endpoint
- **URL**: `/AutoCRM/FindContacts/findByEmail`
- **Method**: POST
- **Payload**: `{"email": "<EMAIL>"}`
- **Response**: `{"total": 2, "list": [{"id": "1", "name": "John Doe", "emailAddress": "<EMAIL>"}]}`

### Security
- Checks user permissions for Contact entity access
- Validates email input
- Proper error handling and user feedback

## Troubleshooting

### Button Not Appearing
1. Clear cache: Administration → Clear Cache
2. Rebuild: Administration → Rebuild
3. Check browser console for JavaScript errors

### Search Not Working
1. Verify user has read access to Contact entity
2. Check server logs for PHP errors
3. Ensure email address exists on the lead

### Navigation Issues
1. Verify Contact entity is accessible
2. Check if text search is enabled for contacts

## Uninstallation

1. Go to Administration → Extensions
2. Find "AutoCRM" in the list
3. Click "Uninstall"
4. Clear cache and rebuild

## Support

This extension was created as a professional implementation following EspoCRM development standards. The code is clean, well-documented, and follows best practices for maintainability and security.

## Version History

- **v1.0.0**: Initial release with Find Contacts functionality
  - Lead detail view button
  - Contact search by email
  - Alert dialog with results
  - Navigation to filtered contact list
  - English and Czech translations
