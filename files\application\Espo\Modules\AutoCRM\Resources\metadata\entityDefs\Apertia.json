{"fields": {"name": {"type": "<PERSON><PERSON><PERSON>", "required": true, "maxLength": 255, "trim": true}, "status": {"type": "enum", "options": ["Draft", "Active", "Completed", "Canceled"], "default": "Draft", "style": {"Draft": "default", "Active": "success", "Completed": "primary", "Canceled": "danger"}}, "description": {"type": "text"}, "assignedUser": {"type": "link"}, "teams": {"type": "linkMultiple"}, "createdAt": {"type": "datetime", "readOnly": true}, "modifiedAt": {"type": "datetime", "readOnly": true}, "createdBy": {"type": "link", "readOnly": true}, "modifiedBy": {"type": "link", "readOnly": true}}, "links": {"assignedUser": {"type": "belongsTo", "entity": "User"}, "teams": {"type": "hasMany", "entity": "Team", "relationName": "EntityTeam"}, "createdBy": {"type": "belongsTo", "entity": "User"}, "modifiedBy": {"type": "belongsTo", "entity": "User"}}, "collection": {"orderBy": "createdAt", "order": "desc"}, "indexes": {"name": {"columns": ["name", "deleted"]}, "assignedUser": {"columns": ["assignedUserId", "deleted"]}}}