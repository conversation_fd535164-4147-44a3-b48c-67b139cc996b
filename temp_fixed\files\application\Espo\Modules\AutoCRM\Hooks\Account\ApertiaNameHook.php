<?php
/************************************************************************
 * This file is part of AutoCRM extension.
 *
 * AutoCRM is free software: you can redistribute it and/or modify
 * it under the terms of the MIT License.
 *
 * @package   AutoCRM
 * <AUTHOR> Development Team
 * @copyright 2025 AutoCRM
 * @license   MIT
 ************************************************************************/

declare(strict_types=1);

namespace Espo\Modules\AutoCRM\Hooks\Account;

use Espo\ORM\Entity;

/**
 * Account Apertia Naming Hook
 *
 * Automatically appends Apertia suffix to Account entity names
 * following professional naming conventions and business rules.
 */
class ApertiaNameHook
{
    /** @var int Hook execution order */
    public static $order = 5;

    /**
     * Before save hook implementation
     */
    public function beforeSave(Entity $entity, array $options = []): void
    {
        // Validate entity type
        if ($entity->getEntityType() !== 'Account') {
            return;
        }

        // Skip if this is a system operation
        if ($this->isSystemOperation($options)) {
            return;
        }

        try {
            $this->processNaming($entity);
        } catch (\Throwable $e) {
            // Log error but don't break the save operation
            error_log('AutoCRM Account Hook Error: ' . $e->getMessage());
        }
    }

    /**
     * Process entity naming logic
     */
    private function processNaming(Entity $entity): void
    {
        $name = $entity->get('name');
        $suffix = ' - Apertia';

        // Handle empty names
        if (empty($name) || trim($name) === '') {
            $entity->set('name', 'Ukázka - Apertia');
            return;
        }

        // Clean and validate name
        $cleanName = trim($name);

        // Apply suffix if not already present
        if (!$this->hasSuffix($cleanName, $suffix)) {
            $entity->set('name', $cleanName . $suffix);
        }
    }

    /**
     * Check if name already has the suffix
     */
    private function hasSuffix(string $name, string $suffix): bool
    {
        $suffixLength = strlen($suffix);
        return strlen($name) >= $suffixLength &&
               substr($name, -$suffixLength) === $suffix;
    }

    /**
     * Check if this is a system operation that should be skipped
     */
    private function isSystemOperation(array $options): bool
    {
        return isset($options['skipHooks']) && $options['skipHooks'] === true ||
               isset($options['silent']) && $options['silent'] === true ||
               isset($options['import']) && $options['import'] === true;
    }
}
