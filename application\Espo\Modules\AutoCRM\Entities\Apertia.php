<?php

namespace Espo\Modules\AutoCRM\Entities;

use Espo\Core\ORM\Entity;

class Apertia extends Entity
{
    public const ENTITY_TYPE = 'Apertia';
    
    public const STATUS_DRAFT = 'Draft';
    public const STATUS_ACTIVE = 'Active';
    public const STATUS_COMPLETED = 'Completed';
    public const STATUS_CANCELED = 'Canceled';

    public function getName(): ?string
    {
        return $this->get('name');
    }

    public function setName(?string $name): self
    {
        $this->set('name', $name);
        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->get('status');
    }

    public function setStatus(?string $status): self
    {
        $this->set('status', $status);
        return $this;
    }

    public function getAssignedUserId(): ?string
    {
        return $this->get('assignedUserId');
    }

    public function setAssignedUserId(?string $assignedUserId): self
    {
        $this->set('assignedUserId', $assignedUserId);
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->get('description');
    }

    public function setDescription(?string $description): self
    {
        $this->set('description', $description);
        return $this;
    }
}
