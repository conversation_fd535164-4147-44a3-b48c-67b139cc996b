# Quick Test Instructions for AutoCRM Extension

## Issues Fixed

1. **Error 500 Fixed**: Changed controller to extend `RecordBase` instead of `Base` to get proper ACL access
2. **Button Location Fixed**: Moved "Find Contacts" button to dropdown menu (three dots) instead of main button area
3. **Search Filter Fixed**: Updated navigation to use simple URL pattern that EspoCRM recognizes for text filtering

## Installation Steps

1. **Uninstall Previous Version** (if installed):
   - Go to Administration → Extensions
   - Find "AutoCRM" and click "Uninstall"
   - Clear cache: Administration → Clear Cache

2. **Install New Version**:
   - Go to Administration → Extensions
   - Click "Upload" and select the new `AutoCRM.zip`
   - Click "Install"
   - Clear cache: Administration → Clear Cache
   - Rebuild: Administration → Rebuild

## Testing

1. **Create Test Data**:
   - Create a Lead with email: `<EMAIL>`
   - Create 1-2 Contacts with the same email: `<EMAIL>`

2. **Test the Feature**:
   - Go to the Lead detail view
   - Click the three dots (⋯) next to the Edit button
   - Look for "Find Contacts" in the dropdown menu
   - Click "Find Contacts"
   - Should show alert with count and names of contacts
   - Click "Go to Contacts" to navigate to filtered contact list

## Expected Behavior

- **No Error 500**: The button should work without server errors
- **Button in Dropdown**: Button appears in the dropdown menu, not main button area
- **Search Results**: Shows alert with contact count and names
- **Navigation**: "Go to Contacts" button navigates to contact list with email filter applied
- **Filtered Results**: Contact list shows only contacts with the matching email address

## If Issues Persist

1. Check server logs: `data/logs/espo-[date].log`
2. Check browser console for JavaScript errors
3. Verify user has read access to Contact entity
4. Clear browser cache and try again

## Technical Changes Made

### Backend Controller (`FindContacts.php`)
- Changed from `extends Base` to `extends RecordBase`
- Removed manual EntityManager injection
- Now uses `$this->getEntityManager()` method
- ACL access now works properly via `$this->getAcl()`

### Frontend JavaScript (`detail.js`)
- Changed from `this.addButton()` to `this.addDropdownItem()`
- Button now appears in dropdown menu instead of main button area
- Updated navigation to use simple URL pattern: `#Contact/list/textFilter=email`
- This follows EspoCRM's standard URL routing for text filters

The extension should now work correctly without the Error 500, with the button in the proper location, and with proper email filtering when navigating to contacts.
