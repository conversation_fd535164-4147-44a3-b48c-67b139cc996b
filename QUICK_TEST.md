# Quick Test Instructions for AutoCRM Extension

## Features Included

1. **Find Contacts Functionality**: Search for contacts with matching email from Lead detail page
2. **Account Name Hook**: Automatically appends "- Apertia" to Account names when creating or updating

## Issues Fixed

1. **Error 500 Fixed**: Changed controller to extend `RecordBase` instead of `Base` to get proper ACL access
2. **Button Location Fixed**: Moved "Find Contacts" button to dropdown menu (three dots) instead of main button area
3. **Search Filter Fixed**: Changed to programmatically fill search bar and trigger search after navigation
4. **Alert Style Simplified**: Changed to simple browser alert() that clearly shows contact information
5. **Account Hook Added**: BeforeSave hook automatically modifies Account names

## Installation Steps

1. **Uninstall Previous Version** (if installed):
   - Go to Administration → Extensions
   - Find "AutoCRM" and click "Uninstall"
   - Clear cache: Administration → Clear Cache

2. **Install New Version**:
   - Go to Administration → Extensions
   - Click "Upload" and select the new `AutoCRM.zip`
   - Click "Install"
   - Clear cache: Administration → Clear Cache
   - Rebuild: Administration → Rebuild

## Testing

1. **Create Test Data**:
   - Create a Lead with email: `<EMAIL>`
   - Create 1-2 Contacts with the same email: `<EMAIL>`

2. **Test Find Contacts Feature**:
   - Go to the Lead detail view
   - Click the three dots (⋯) next to the Edit button
   - Look for "Find Contacts" in the dropdown menu
   - Click "Find Contacts"
   - Should show alert with count and names of contacts
   - Click "Go to Contacts" to navigate to filtered contact list

3. **Test Account Name Hook**:
   - Go to Accounts → Create Account
   - Enter name: "Test Company"
   - Save the account
   - Verify: Name should automatically become "Test Company - Apertia"
   - Edit the account and change name to "New Name - Apertia"
   - Save again
   - Verify: Name should remain "New Name - Apertia" (no duplicate suffix)
   - Create account with empty name
   - Verify: Name should become "Ukázka - Apertia"

## Expected Behavior

- **No Error 500**: The button should work without server errors
- **Button in Dropdown**: Button appears in the dropdown menu, not main button area
- **Search Results**: Shows browser alert with contact count and names clearly displayed
- **Automatic Navigation**: After clicking OK on alert, automatically navigates to contact list
- **Filtered Results**: Contact list shows email in search bar and only matching contacts
- **Account Name Hook**: Account names automatically get "- Apertia" suffix when saved
- **No Duplicate Suffixes**: Hook prevents adding multiple "- Apertia" suffixes

## If Issues Persist

1. Check server logs: `data/logs/espo-[date].log`
2. Check browser console for JavaScript errors
3. Verify user has read access to Contact entity
4. Clear browser cache and try again

## Technical Changes Made

### Backend Controller (`FindContacts.php`)
- Changed from `extends Base` to `extends RecordBase`
- Removed manual EntityManager injection
- Now uses `$this->getEntityManager()` method
- ACL access now works properly via `$this->getAcl()`

### Frontend JavaScript (`detail.js`)
- Changed from `this.addButton()` to `this.addDropdownItem()`
- Button now appears in dropdown menu instead of main button area
- Replaced complex modal with simple browser alert() for guaranteed visibility
- Alert shows contact count, email address, and list of contact names
- After clicking OK, automatically navigates to contacts
- Added programmatic search functionality that fills the search bar and triggers search
- Uses DOM manipulation to interact with EspoCRM's search interface after navigation

### Account Hook (`ApertiaNameHook.php`)
- BeforeSave hook for Account entity
- Automatically appends "- Apertia" to account names
- Handles empty names by setting to "Ukázka - Apertia"
- Prevents duplicate suffixes using string checking
- Uses proper EspoCRM hook interfaces and conventions

The extension should now work correctly with both Find Contacts functionality and automatic Account naming.
