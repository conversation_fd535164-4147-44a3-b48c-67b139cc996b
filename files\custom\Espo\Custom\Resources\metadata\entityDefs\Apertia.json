{"fields": {"name": {"type": "<PERSON><PERSON><PERSON>", "required": true, "pattern": "$noBadCharacters"}, "description": {"type": "text"}, "createdAt": {"type": "datetime", "readOnly": true}, "modifiedAt": {"type": "datetime", "readOnly": true}, "createdBy": {"type": "link", "readOnly": true, "view": "views/fields/user"}, "modifiedBy": {"type": "link", "readOnly": true, "view": "views/fields/user"}, "assignedUser": {"type": "link", "required": false, "view": "views/fields/assigned-user"}, "teams": {"type": "linkMultiple", "view": "views/fields/teams"}}, "links": {"createdBy": {"type": "belongsTo", "entity": "User"}, "modifiedBy": {"type": "belongsTo", "entity": "User"}, "assignedUser": {"type": "belongsTo", "entity": "User"}, "teams": {"type": "hasMany", "entity": "Team", "relationName": "entityTeam", "layoutRelationshipsDisabled": true}}, "collection": {"orderBy": "createdAt", "order": "desc"}, "indexes": {"name": {"columns": ["name", "deleted"]}, "assignedUser": {"columns": ["assignedUserId", "deleted"]}, "createdAt": {"columns": ["createdAt"]}, "createdAtId": {"unique": true, "columns": ["createdAt", "id"]}}}