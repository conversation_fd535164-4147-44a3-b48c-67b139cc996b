<?php
/************************************************************************
 * This file is part of AutoCRM extension.
 *
 * AutoCRM is free software: you can redistribute it and/or modify
 * it under the terms of the MIT License.
 *
 * @package   AutoCRM
 * <AUTHOR> Development Team
 * @copyright 2025 AutoCRM
 * @license   MIT
 ************************************************************************/

declare(strict_types=1);

namespace Espo\Modules\AutoCRM;

use Espo\Core\Utils\Config;
use Espo\Core\Utils\Config\ConfigWriter;
use Espo\Core\InjectableFactory;
use Espo\Modules\AutoCRM\Core\Config\AutoCRMConfig;
use Espo\Modules\AutoCRM\Core\Utils\Logger;

/**
 * AutoCRM Setup Manager
 *
 * Handles extension installation, uninstallation, and configuration
 * with comprehensive error handling and logging.
 */
class Setup
{
    private InjectableFactory $injectableFactory;
    private AutoCRMConfig $config;
    private Logger $logger;

    public function __construct(InjectableFactory $injectableFactory)
    {
        $this->injectableFactory = $injectableFactory;
        $this->config = new AutoCRMConfig();
        $this->logger = new Logger(
            $this->injectableFactory->create(\Espo\Core\Utils\Log::class),
            'Setup'
        );
    }

    /**
     * Called after extension installation
     */
    public function afterInstall(): void
    {
        $startTime = microtime(true);

        try {
            $this->logger->info('Starting AutoCRM installation process');

            $this->validateSystemRequirements();
            $this->addToNavigation();
            $this->initializeConfiguration();
            $this->clearCache();

            $this->logger->info('AutoCRM installation completed successfully');

        } catch (\Throwable $e) {
            $this->logger->logException($e, ['operation' => 'install']);
            throw new \RuntimeException('AutoCRM installation failed: ' . $e->getMessage(), 0, $e);
        } finally {
            $executionTime = microtime(true) - $startTime;
            $this->logger->logPerformance('installation', $executionTime);
        }
    }

    /**
     * Called after extension uninstallation
     */
    public function afterUninstall(): void
    {
        $startTime = microtime(true);

        try {
            $this->logger->info('Starting AutoCRM uninstallation process');

            $this->removeFromNavigation();
            $this->cleanupConfiguration();
            $this->clearCache();

            $this->logger->info('AutoCRM uninstallation completed successfully');

        } catch (\Throwable $e) {
            $this->logger->logException($e, ['operation' => 'uninstall']);
            // Don't throw during uninstall to avoid blocking the process
        } finally {
            $executionTime = microtime(true) - $startTime;
            $this->logger->logPerformance('uninstallation', $executionTime);
        }
    }

    /**
     * Validate system requirements
     */
    private function validateSystemRequirements(): void
    {
        // Check PHP version
        if (version_compare(PHP_VERSION, '8.1.0', '<')) {
            throw new \RuntimeException('AutoCRM requires PHP 8.1 or higher. Current version: ' . PHP_VERSION);
        }

        // Check required PHP extensions
        $requiredExtensions = ['json', 'mbstring', 'openssl', 'pdo'];
        foreach ($requiredExtensions as $extension) {
            if (!extension_loaded($extension)) {
                throw new \RuntimeException("Required PHP extension '{$extension}' is not loaded");
            }
        }

        $this->logger->info('System requirements validation passed');
    }

    /**
     * Initialize AutoCRM configuration
     */
    private function initializeConfiguration(): void
    {
        try {
            $configWriter = $this->injectableFactory->create(ConfigWriter::class);

            // Set default AutoCRM configuration
            $configWriter->set('autoCrmEnabled', true);
            $configWriter->set('autoCrmVersion', '2.0.0');
            $configWriter->set('autoCrmInstallDate', date('Y-m-d H:i:s'));
            $configWriter->save();

            $this->logger->info('AutoCRM configuration initialized');

        } catch (\Throwable $e) {
            $this->logger->logException($e, ['operation' => 'initialize_config']);
            throw new \RuntimeException('Failed to initialize AutoCRM configuration: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Add Apertia entity to navigation
     */
    private function addToNavigation(): void
    {
        try {
            $config = $this->injectableFactory->create(Config::class);
            $configWriter = $this->injectableFactory->create(ConfigWriter::class);

            $tabList = $config->get('tabList', []);
            $originalTabList = $tabList;

            // Add Apertia to navigation if not already present
            if (!in_array('Apertia', $tabList, true)) {
                $navPrefs = $this->config->getNavigationPreferences();
                $insertPosition = count($tabList);

                // Try to find preferred position
                if (isset($navPrefs['insertAfter'])) {
                    $afterIndex = array_search($navPrefs['insertAfter'], $tabList, true);
                    if ($afterIndex !== false) {
                        $insertPosition = $afterIndex + 1;
                    }
                }

                // Insert Apertia at the calculated position
                array_splice($tabList, $insertPosition, 0, ['Apertia']);

                $configWriter->set('tabList', $tabList);
                $configWriter->save();

                $this->logger->logConfigChange('tabList', $originalTabList, $tabList);
                $this->logger->info('Apertia entity added to navigation', [
                    'position' => $insertPosition,
                    'total_tabs' => count($tabList)
                ]);
            } else {
                $this->logger->info('Apertia entity already exists in navigation');
            }
        } catch (\Throwable $e) {
            $this->logger->logException($e, ['operation' => 'add_navigation']);
            throw new \RuntimeException('Failed to add Apertia to navigation: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Remove Apertia entity from navigation
     */
    private function removeFromNavigation(): void
    {
        try {
            $config = $this->injectableFactory->create(Config::class);
            $configWriter = $this->injectableFactory->create(ConfigWriter::class);

            $tabList = $config->get('tabList', []);
            $originalTabList = $tabList;

            // Remove Apertia from navigation if present
            $apertiaIndex = array_search('Apertia', $tabList, true);
            if ($apertiaIndex !== false) {
                array_splice($tabList, $apertiaIndex, 1);

                $configWriter->set('tabList', $tabList);
                $configWriter->save();

                $this->logger->logConfigChange('tabList', $originalTabList, $tabList);
                $this->logger->info('Apertia entity removed from navigation');
            } else {
                $this->logger->info('Apertia entity not found in navigation');
            }
        } catch (\Throwable $e) {
            $this->logger->logException($e, ['operation' => 'remove_navigation']);
            // Don't throw during uninstall
        }
    }

    /**
     * Cleanup AutoCRM configuration
     */
    private function cleanupConfiguration(): void
    {
        try {
            $configWriter = $this->injectableFactory->create(ConfigWriter::class);

            // Remove AutoCRM configuration
            $configWriter->remove('autoCrmEnabled');
            $configWriter->remove('autoCrmVersion');
            $configWriter->remove('autoCrmInstallDate');
            $configWriter->save();

            $this->logger->info('AutoCRM configuration cleaned up');

        } catch (\Throwable $e) {
            $this->logger->logException($e, ['operation' => 'cleanup_config']);
            // Don't throw during uninstall
        }
    }

    /**
     * Clear cache after installation/uninstallation
     */
    private function clearCache(): void
    {
        try {
            $configWriter = $this->injectableFactory->create(ConfigWriter::class);

            // Force cache rebuild
            $configWriter->set('cacheTimestamp', time());
            $configWriter->save();

            $this->logger->info('Cache cleared successfully');

        } catch (\Throwable $e) {
            $this->logger->logException($e, ['operation' => 'clear_cache']);
            throw new \RuntimeException('Failed to clear cache: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Get installation status and metadata
     */
    public function getStatus(): array
    {
        try {
            $config = $this->injectableFactory->create(Config::class);

            return [
                'installed' => $config->get('autoCrmEnabled', false),
                'version' => $config->get('autoCrmVersion', 'unknown'),
                'install_date' => $config->get('autoCrmInstallDate', 'unknown'),
                'entities_enabled' => $this->config->getEnabledEntities(),
                'navigation_configured' => in_array('Apertia', $config->get('tabList', []), true),
                'system_info' => [
                    'php_version' => PHP_VERSION,
                    'memory_limit' => ini_get('memory_limit'),
                    'max_execution_time' => ini_get('max_execution_time')
                ]
            ];
        } catch (\Throwable $e) {
            $this->logger->logException($e, ['operation' => 'get_status']);
            return ['error' => 'Failed to get status: ' . $e->getMessage()];
        }
    }
}
