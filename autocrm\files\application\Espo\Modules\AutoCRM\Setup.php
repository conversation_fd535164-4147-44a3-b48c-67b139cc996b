<?php
/************************************************************************
 * This file is part of AutoCRM extension.
 ************************************************************************/

namespace Espo\Modules\AutoCRM;

use Espo\Core\Utils\Config;
use Espo\Core\Utils\Config\ConfigWriter;
use Espo\Core\InjectableFactory;

class Setup
{
    private InjectableFactory $injectableFactory;

    public function __construct(InjectableFactory $injectableFactory)
    {
        $this->injectableFactory = $injectableFactory;
    }

    /**
     * Called after extension installation
     */
    public function afterInstall(): void
    {
        $this->addToNavigation();
        $this->clearCache();
    }

    /**
     * Called after extension uninstallation
     */
    public function afterUninstall(): void
    {
        $this->clearCache();
    }

    /**
     * Add Apertia entity to navigation
     */
    private function addToNavigation(): void
    {
        try {
            $config = $this->injectableFactory->create(Config::class);
            $configWriter = $this->injectableFactory->create(ConfigWriter::class);

            $tabList = $config->get('tabList', []);

            // Add Apertia to navigation if not already present
            if (!in_array('Apertia', $tabList)) {
                // Find position after Account or at the end
                $insertPosition = count($tabList);
                $accountIndex = array_search('Account', $tabList);
                if ($accountIndex !== false) {
                    $insertPosition = $accountIndex + 1;
                }

                // Insert Apertia at the calculated position
                array_splice($tabList, $insertPosition, 0, ['Apertia']);

                $configWriter->set('tabList', $tabList);
                $configWriter->save();
            }
        } catch (\Exception $e) {
            // Log error but don't fail installation
            error_log('AutoCRM Setup: Failed to add navigation - ' . $e->getMessage());
        }
    }

    /**
     * Clear cache after installation/uninstallation
     */
    private function clearCache(): void
    {
        try {
            $config = $this->injectableFactory->create(Config::class);
            $configWriter = $this->injectableFactory->create(ConfigWriter::class);

            // Force cache rebuild
            $configWriter->set('cacheTimestamp', time());
            $configWriter->save();
        } catch (\Exception $e) {
            // Log error but don't fail installation
            error_log('AutoCRM Setup: Failed to clear cache - ' . $e->getMessage());
        }
    }
}
