<?php
/************************************************************************
 * This file is part of AutoCRM extension.
 ************************************************************************/

namespace Espo\Modules\AutoCRM\Controllers;

use Espo\Core\Api\Request;
use Espo\Core\Api\Response;
use Espo\Core\Controllers\Base;
use Espo\Core\Exceptions\BadRequest;
use Espo\Core\Exceptions\Forbidden;
use Espo\Core\Utils\Json;
use Espo\ORM\EntityManager;

class FindContacts extends Base
{
    private EntityManager $entityManager;

    public function __construct(EntityManager $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    /**
     * Find contacts by email address
     */
    public function postActionFindByEmail(Request $request, Response $response): Response
    {
        if (!$this->acl->checkScope('Contact', 'read')) {
            throw new Forbidden('No access to Contact entity.');
        }

        $data = $request->getParsedBody();
        $email = $data->email ?? null;

        if (!$email) {
            throw new BadRequest('Email address is required.');
        }

        // Validate email format
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new BadRequest('Invalid email address format.');
        }

        // Search for contacts with the given email address
        $contacts = $this->entityManager
            ->getRDBRepository('Contact')
            ->select(['id', 'name', 'emailAddress', 'firstName', 'lastName'])
            ->where([
                'emailAddress' => $email,
                'deleted' => false
            ])
            ->limit(0, 50) // Limit to 50 results for performance
            ->find();

        $result = [];
        foreach ($contacts as $contact) {
            $name = $contact->get('name');
            if (!$name) {
                // Fallback to firstName + lastName if name is empty
                $firstName = $contact->get('firstName');
                $lastName = $contact->get('lastName');
                $name = trim(($firstName ?? '') . ' ' . ($lastName ?? ''));
                if (!$name) {
                    $name = 'Unnamed Contact';
                }
            }

            $result[] = [
                'id' => $contact->getId(),
                'name' => $name,
                'emailAddress' => $contact->get('emailAddress')
            ];
        }

        return $response->writeBody(Json::encode([
            'total' => count($result),
            'list' => $result
        ]));
    }
}
