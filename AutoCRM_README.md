# AutoCRM Extension v3.0.0

[![Version](https://img.shields.io/badge/version-3.0.0-blue.svg)](https://github.com/autocrm/espocrm-extension)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![EspoCRM](https://img.shields.io/badge/EspoCRM-8.0%2B-orange.svg)](https://www.espocrm.com/)
[![PHP](https://img.shields.io/badge/PHP-8.1%2B-purple.svg)](https://php.net/)

**Simple, Reliable, Professional** - AutoCRM extension optimized for production use with minimal complexity and maximum reliability.

## 🎯 Key Features

- **Custom Apertia Entity**: Complete CRUD operations with professional UI
- **Automated Naming**: Intelligent " - Apertia" suffix for Account and Apertia entities
- **Modern Architecture**: Uses EspoCRM 8+ standards with beforeSave hooks
- **Zero Dependencies**: No complex dependencies or configurations
- **Error Resilient**: Comprehensive error handling that never breaks functionality
- **Multi-language**: English and Czech language support

## 🚀 What's New in v3.0.0

### ✅ **Simplified Architecture**
- Removed complex dependency injection
- Eliminated over-engineering
- Reduced file count by 60%
- Streamlined for reliability

### ✅ **Modern Standards**
- Uses `beforeSave` hooks (no recursive save issues)
- PHP 8.1+ with strict typing
- Modern `str_ends_with()` function
- Professional error handling

### ✅ **Production Ready**
- Zero configuration needed
- Works out-of-the-box
- Comprehensive error protection
- Easy to maintain and debug

## 📋 Requirements

- **EspoCRM**: 8.0 or higher
- **PHP**: 8.1 or higher
- **Memory**: 128MB minimum

## 🔧 Installation

1. Download `AutoCRM_Simple.zip`
2. Go to **Administration > Extensions**
3. Upload and install the extension
4. Clear cache: **Administration > Clear Cache**
5. **Done!** - Apertia appears in navigation automatically

## 💡 How It Works

### Automatic Naming
- **Account names** automatically get " - Apertia" suffix
- **Apertia names** automatically get " - Apertia" suffix  
- **Empty names** become "Ukázka - Apertia"
- **No duplicates** - suffix only added if not present

### Smart Hook System
- Uses `beforeSave` to prevent recursive saves
- Skips system operations (imports, etc.)
- Error-protected - never breaks save operations
- Modern PHP 8.1+ implementation

## 🏗️ Architecture

### Simple & Reliable
```
AutoCRM/
├── Controllers/Apertia.php          # Simple Record controller
├── Entities/Apertia.php             # Basic entity with getters/setters
├── Repositories/Apertia.php         # Simple Database repository
├── Hooks/Account/ApertiaNameHook.php # beforeSave hook
├── Hooks/Apertia/ApertiaNameHook.php # beforeSave hook
├── Resources/metadata/              # Entity definitions
├── Resources/layouts/               # UI layouts
├── Resources/i18n/                  # Translations
└── Setup.php                        # Simple installation
```

### Key Design Principles
1. **Simplicity**: Minimal code, maximum reliability
2. **Standards**: Modern EspoCRM 8+ practices
3. **Safety**: Error protection everywhere
4. **Performance**: Fast, lightweight implementation

## 🔍 Troubleshooting

### Common Issues
- **Hooks not working**: Clear cache and rebuild
- **Navigation missing**: Manually add via Administration > User Interface
- **Naming not applied**: Check entity type and hook execution

### Debug Information
All errors are logged to EspoCRM error log with prefix "AutoCRM".

## 📈 Changelog

### v3.0.0 (2025-01-15)
- ✨ Simplified architecture (60% fewer files)
- 🚀 Improved reliability and performance
- 🔧 Modern PHP 8.1+ implementation
- 🛡️ Enhanced error handling
- 📚 Professional documentation

### v2.0.0 (Previous)
- Complex architecture with extensive features
- Over-engineered for simple functionality

### v1.0.0 (Original)
- Basic functionality

## 🤝 Contributing

This extension is designed for simplicity and reliability. When contributing:
1. Keep it simple
2. Follow existing patterns
3. Add comprehensive error handling
4. Test thoroughly

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Issues**: Create GitHub issue with detailed description
- **Questions**: Check EspoCRM forum
- **Documentation**: This README contains all necessary information

---

**AutoCRM v3.0.0** - *Simple, Reliable, Professional*
