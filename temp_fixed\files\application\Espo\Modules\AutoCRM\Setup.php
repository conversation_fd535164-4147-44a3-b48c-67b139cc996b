<?php
/************************************************************************
 * This file is part of AutoCRM extension.
 *
 * AutoCRM is free software: you can redistribute it and/or modify
 * it under the terms of the MIT License.
 *
 * @package   AutoCRM
 * <AUTHOR> Development Team
 * @copyright 2025 AutoCRM
 * @license   MIT
 ************************************************************************/

declare(strict_types=1);

namespace Espo\Modules\AutoCRM;

use Espo\Core\Utils\Config;
use Espo\Core\Utils\Config\ConfigWriter;
use Espo\Core\InjectableFactory;

/**
 * AutoCRM Setup Manager
 *
 * Handles extension installation, uninstallation, and configuration
 * with comprehensive error handling and logging.
 */
class Setup
{
    private InjectableFactory $injectableFactory;

    public function __construct(InjectableFactory $injectableFactory)
    {
        $this->injectableFactory = $injectableFactory;
    }

    /**
     * Called after extension installation
     */
    public function afterInstall(): void
    {
        try {
            $this->addToNavigation();
            $this->clearCache();
        } catch (\Throwable $e) {
            error_log('AutoCRM Setup: Installation failed - ' . $e->getMessage());
            // Don't throw to avoid breaking installation
        }
    }

    /**
     * Called after extension uninstallation
     */
    public function afterUninstall(): void
    {
        try {
            $this->removeFromNavigation();
            $this->clearCache();
        } catch (\Throwable $e) {
            error_log('AutoCRM Setup: Uninstallation failed - ' . $e->getMessage());
            // Don't throw during uninstall to avoid blocking the process
        }
    }



    /**
     * Add Apertia entity to navigation
     */
    private function addToNavigation(): void
    {
        try {
            $config = $this->injectableFactory->create(Config::class);
            $configWriter = $this->injectableFactory->create(ConfigWriter::class);

            $tabList = $config->get('tabList', []);

            // Add Apertia to navigation if not already present
            if (!in_array('Apertia', $tabList, true)) {
                $insertPosition = count($tabList);

                // Try to find position after Account
                $accountIndex = array_search('Account', $tabList, true);
                if ($accountIndex !== false) {
                    $insertPosition = $accountIndex + 1;
                }

                // Insert Apertia at the calculated position
                array_splice($tabList, $insertPosition, 0, ['Apertia']);

                $configWriter->set('tabList', $tabList);
                $configWriter->save();
            }
        } catch (\Throwable $e) {
            error_log('AutoCRM Setup: Failed to add navigation - ' . $e->getMessage());
        }
    }

    /**
     * Remove Apertia entity from navigation
     */
    private function removeFromNavigation(): void
    {
        try {
            $config = $this->injectableFactory->create(Config::class);
            $configWriter = $this->injectableFactory->create(ConfigWriter::class);

            $tabList = $config->get('tabList', []);

            // Remove Apertia from navigation if present
            $apertiaIndex = array_search('Apertia', $tabList, true);
            if ($apertiaIndex !== false) {
                array_splice($tabList, $apertiaIndex, 1);

                $configWriter->set('tabList', $tabList);
                $configWriter->save();
            }
        } catch (\Throwable $e) {
            error_log('AutoCRM Setup: Failed to remove navigation - ' . $e->getMessage());
        }
    }

    /**
     * Clear cache after installation/uninstallation
     */
    private function clearCache(): void
    {
        try {
            $configWriter = $this->injectableFactory->create(ConfigWriter::class);

            // Force cache rebuild
            $configWriter->set('cacheTimestamp', time());
            $configWriter->save();
        } catch (\Throwable $e) {
            error_log('AutoCRM Setup: Failed to clear cache - ' . $e->getMessage());
        }
    }
}
