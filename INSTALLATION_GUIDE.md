# AutoCRM Extension - Installation and Testing Guide

## Quick Installation Steps

1. **Upload Extension**:
   - Go to Administration → Extensions
   - Click "Upload" button
   - Select the `AutoCRM.zip` file
   - Click "Install"

2. **Clear Cache & Rebuild**:
   - Go to Administration → Clear Cache
   - Go to Administration → Rebuild

3. **Test the Feature**:
   - Navigate to any Lead with an email address
   - Look for the "Find Contacts" button
   - Click it to search for contacts with the same email

## What the Extension Does

### Frontend (JavaScript)
- Adds a "Find Contacts" button to Lead detail view
- Validates email address format
- Makes AJAX call to backend API
- Shows results in alert/confirmation dialog
- Navigates to Contacts list with email filter

### Backend (PHP)
- Provides REST API endpoint: `POST /AutoCRM/FindContacts/findByEmail`
- Validates user permissions and email format
- Searches Contact entity for matching email addresses
- Returns JSON response with count and contact details
- Handles edge cases (unnamed contacts, performance limits)

### Features
- **Security**: Checks user permissions for Contact entity access
- **Validation**: Email format validation on both frontend and backend
- **Performance**: Limits search results to 50 contacts
- **User Experience**: Clear feedback messages and navigation
- **Internationalization**: English and Czech translations
- **Error Handling**: Comprehensive error handling and user feedback

## File Structure Created

```
AutoCRM.zip
├── manifest.json                                    # Extension metadata
└── files/
    ├── application/Espo/Modules/AutoCRM/
    │   ├── Controllers/FindContacts.php            # API controller
    │   ├── Setup.php                               # Installation hooks
    │   └── Resources/
    │       ├── routes.json                         # API routes
    │       ├── metadata/clientDefs/Lead.json       # Lead view configuration
    │       └── i18n/                               # Translations
    │           ├── en_US/AutoCRM.json              # English
    │           └── cs_CZ/AutoCRM.json              # Czech
    └── client/custom/modules/autocrm/src/views/lead/record/
        └── detail.js                               # Extended Lead detail view
```

## Testing Scenarios

### 1. Basic Functionality Test
- Create a Lead with email: `<EMAIL>`
- Create 1-2 Contacts with the same email
- Go to Lead detail view
- Click "Find Contacts" button
- Verify: Alert shows correct count and names
- Click "Go to Contacts" and verify navigation works

### 2. No Results Test
- Create a Lead with unique email: `<EMAIL>`
- Ensure no Contacts have this email
- Click "Find Contacts" button
- Verify: Alert shows "No contacts found" message

### 3. No Email Test
- Create a Lead without email address
- Click "Find Contacts" button
- Verify: Error message "No email address found"

### 4. Invalid Email Test
- Create a Lead with invalid email: `invalid-email`
- Click "Find Contacts" button
- Verify: Error message "Invalid email address format"

### 5. Permission Test
- Login as user without Contact read access
- Try to use "Find Contacts" button
- Verify: Proper error handling

## Troubleshooting

### Button Not Visible
1. Clear cache: Administration → Clear Cache
2. Rebuild: Administration → Rebuild
3. Check browser console for JavaScript errors
4. Verify extension is installed: Administration → Extensions

### API Errors
1. Check server logs for PHP errors
2. Verify user has Contact read permissions
3. Test API endpoint directly: `POST /AutoCRM/FindContacts/findByEmail`

### Navigation Issues
1. Verify Contact entity is accessible to user
2. Check if text search is enabled for Contact entity
3. Verify URL encoding is working properly

## Uninstallation

1. Go to Administration → Extensions
2. Find "AutoCRM" extension
3. Click "Uninstall"
4. Clear cache and rebuild

## Professional Implementation Notes

This extension follows EspoCRM best practices:
- Proper module structure and namespacing
- Security checks and validation
- Clean separation of frontend/backend logic
- Comprehensive error handling
- Internationalization support
- Performance considerations
- Professional code documentation
